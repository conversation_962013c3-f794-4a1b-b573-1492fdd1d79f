from flask import Flask, jsonify
import util

app = Flask(__name__)

def handler(request):
    """Vercel serverless function handler"""
    response = jsonify({
        'locations': util.get_location_names()
    })
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
    return response

# For local testing
@app.route('/')
def get_location_names():
    return handler(None)

if __name__ == "__main__":
    app.run(debug=True)
