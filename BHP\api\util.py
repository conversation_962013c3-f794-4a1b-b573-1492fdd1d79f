import json
import pickle
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

__locations = None
__data_columns = None
__model = None

def get_estimated_price(location, total_sqft, bath, bhk):
    try:
        loc_index = __data_columns.index(location.strip().lower())
    except:
        loc_index = -1
    
    a = np.zeros(len(__data_columns))
    a[0] = total_sqft
    a[1] = bath
    a[2] = bhk
    if loc_index >= 0:
        a[loc_index] = 1
    return round(__model.predict([a])[0], 2)

def get_location_names():
    return __locations

def load_saved_artifacts():
    print("loading saved artifacts...start")
    global __data_columns
    global __locations
    global __model
    
    # Get the directory of the current file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Load columns
    columns_path = os.path.join(current_dir, "artifacts", "columns.json")
    with open(columns_path, 'r') as f:
        __data_columns = json.load(f)['data_columns']
        __locations = __data_columns[3:]
    
    # Load model
    model_path = os.path.join(current_dir, "artifacts", "bengaluru_House_Data.pkl")
    with open(model_path, 'rb') as f:
        __model = pickle.load(f)
    
    print("loading saved artifacts ... done")

# Initialize artifacts when module is imported
load_saved_artifacts()
