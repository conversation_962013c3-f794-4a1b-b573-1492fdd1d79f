# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
_not_needed/

# Virtual environments
venv/
env/
ENV/
myenv/
.venv/
.env

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# pyenv
.python-version

# Streamlit
.streamlit/secrets.toml

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Old Flask files (keeping for reference)
server/myenv/
bhp.conf
client/

# Data files
*.csv
*.xlsx
*.xls

# Model files (keep only the ones in artifacts)
*.pkl
!artifacts/*.pkl

# Temporary files
*.tmp
*.temp

# Cache
.cache/
*.cache

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
