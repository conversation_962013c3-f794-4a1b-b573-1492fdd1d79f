import streamlit as st
import json
import pickle
import numpy as np
import pandas as pd
import warnings
import os
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Bangalore House Price Predictor",
    page_icon="🏠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Global variables for model and data
@st.cache_data
def load_artifacts():
    """Load the saved model and location data"""
    try:
        # Load columns data
        with open("artifacts/columns.json", 'r') as f:
            data_columns = json.load(f)['data_columns']
            locations = data_columns[3:]  # First 3 are sqft, bath, bhk

        # Load the trained model
        with open("artifacts/bengaluru_House_Data.pkl", 'rb') as f:
            model = pickle.load(f)
        
        return model, data_columns, locations
    except Exception as e:
        st.error(f"Error loading model artifacts: {e}")
        return None, None, None

def predict_price(location, total_sqft, bath, bhk, model, data_columns):
    """Predict house price based on input parameters"""
    try:
        # Find location index
        try:
            loc_index = data_columns.index(location.strip().lower())
        except:
            loc_index = -1
        
        # Create input array
        x = np.zeros(len(data_columns))
        x[0] = total_sqft
        x[1] = bath
        x[2] = bhk
        if loc_index >= 0:
            x[loc_index] = 1
        
        # Make prediction
        predicted_price = model.predict([x])[0]
        return round(predicted_price, 2)
    except Exception as e:
        st.error(f"Error making prediction: {e}")
        return None

def main():
    # Load model and data
    model, data_columns, locations = load_artifacts()
    
    if model is None:
        st.error("Failed to load model. Please check if the artifacts exist.")
        return
    
    # App header
    st.title("🏠 Bangalore House Price Predictor")
    st.markdown("---")
    st.markdown("### Predict house prices in Bangalore based on location, size, and amenities")
    
    # Create two columns for layout
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Enter Property Details")
        
        # Location selection
        location = st.selectbox(
            "📍 Select Location",
            options=sorted(locations),
            help="Choose the area/locality in Bangalore"
        )
        
        # Area input
        total_sqft = st.number_input(
            "📐 Total Area (Square Feet)",
            min_value=300,
            max_value=10000,
            value=1000,
            step=50,
            help="Enter the total area of the property"
        )
        
        # BHK selection
        bhk = st.selectbox(
            "🛏️ BHK (Bedrooms)",
            options=[1, 2, 3, 4, 5],
            index=1,  # Default to 2 BHK
            help="Number of bedrooms"
        )
        
        # Bathrooms selection
        bath = st.selectbox(
            "🚿 Bathrooms",
            options=[1, 2, 3, 4, 5],
            index=1,  # Default to 2 bathrooms
            help="Number of bathrooms"
        )
        
        # Predict button
        if st.button("🔮 Predict Price", type="primary", use_container_width=True):
            if location and total_sqft and bhk and bath:
                with st.spinner("Calculating price..."):
                    predicted_price = predict_price(location, total_sqft, bath, bhk, model, data_columns)
                
                if predicted_price:
                    st.success(f"### 💰 Estimated Price: ₹{predicted_price:.2f} Lakhs")
                    
                    # Additional insights
                    price_per_sqft = (predicted_price * 100000) / total_sqft
                    st.info(f"**Price per sq ft:** ₹{price_per_sqft:.0f}")
                    
                    # Price range
                    lower_bound = predicted_price * 0.9
                    upper_bound = predicted_price * 1.1
                    st.info(f"**Expected Range:** ₹{lower_bound:.2f} - ₹{upper_bound:.2f} Lakhs")
            else:
                st.error("Please fill in all the details!")
    
    with col2:
        st.subheader("📊 Property Summary")
        
        # Display current selections
        if 'location' in locals():
            st.metric("Location", location)
        if 'total_sqft' in locals():
            st.metric("Area", f"{total_sqft:,} sq ft")
        if 'bhk' in locals():
            st.metric("Bedrooms", f"{bhk} BHK")
        if 'bath' in locals():
            st.metric("Bathrooms", bath)
        
        # Add some tips
        st.subheader("💡 Tips")
        st.markdown("""
        - **Location** greatly affects price
        - **Central areas** are typically more expensive
        - **Price per sq ft** varies by locality
        - Consider **future development** in the area
        """)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center'>
        <p>Built with ❤️ using Streamlit | Data Science Project</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
