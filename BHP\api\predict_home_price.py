from flask import Flask, request, jsonify
import util

app = Flask(__name__)

def handler(request):
    """Vercel serverless function handler"""
    if request.method == 'OPTIONS':
        # Handle CORS preflight request
        response = jsonify({})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        return response
    
    if request.method == 'POST':
        # Handle form data or JSON data
        if request.content_type == 'application/json':
            data = request.get_json()
            location = data.get('location')
            total_sqft = float(data.get('total_sqft'))
            bath = int(data.get('bath'))
            bhk = int(data.get('bhk'))
        else:
            # Handle form data
            location = request.form['location']
            total_sqft = float(request.form['total_sqft'])
            bath = int(request.form['bath'])
            bhk = int(request.form['bhk'])
        
        response = jsonify({
            'estimated_price': util.get_estimated_price(location, total_sqft, bath, bhk)
        })
        
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        return response

# For local testing
@app.route('/', methods=['GET', 'POST', 'OPTIONS'])
def predict_home_price():
    return handler(request)

if __name__ == "__main__":
    app.run(debug=True)
