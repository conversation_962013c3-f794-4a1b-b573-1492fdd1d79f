{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"id": "mUwXwxPKJE1l"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import joblib"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "pQXpGkMUJZLH", "outputId": "b82f5493-0de7-441b-e0f9-b64021491b49"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>area_type</th>\n", "      <th>availability</th>\n", "      <th>location</th>\n", "      <th>size</th>\n", "      <th>society</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>balcony</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Super built-up  Area</td>\n", "      <td>19-Dec</td>\n", "      <td>Electronic City Phase II</td>\n", "      <td>2 BHK</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1056</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>39.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Plot  Area</td>\n", "      <td>Ready To Move</td>\n", "      <td><PERSON><PERSON> T<PERSON></td>\n", "      <td>4 Bedroom</td>\n", "      <td>Theanmp</td>\n", "      <td>2600</td>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>120.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Built-up  Area</td>\n", "      <td>Ready To Move</td>\n", "      <td>Uttar<PERSON><PERSON>i</td>\n", "      <td>3 BHK</td>\n", "      <td>NaN</td>\n", "      <td>1440</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>62.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Super built-up  Area</td>\n", "      <td>Ready To Move</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>3 BHK</td>\n", "      <td>Soiewre</td>\n", "      <td>1521</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>95.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Super built-up  Area</td>\n", "      <td>Ready To Move</td>\n", "      <td>Ko<PERSON><PERSON></td>\n", "      <td>2 BHK</td>\n", "      <td>NaN</td>\n", "      <td>1200</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>51.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              area_type   availability                  location       size  \\\n", "0  Super built-up  Area         19-Dec  Electronic City Phase II      2 BHK   \n", "1            Plot  Area  Ready To Move          Chikka Tirupathi  4 Bedroom   \n", "2        Built-up  Area  Ready To Move               Uttarahalli      3 BHK   \n", "3  Super built-up  Area  Ready To Move        Lingadheeranahalli      3 BHK   \n", "4  Super built-up  Area  Ready To Move                  Kothanur      2 BHK   \n", "\n", "   society total_sqft  bath  balcony   price  \n", "0  Coomee        1056   2.0      1.0   39.07  \n", "1  Theanmp       2600   5.0      3.0  120.00  \n", "2      NaN       1440   2.0      3.0   62.00  \n", "3  Soiewre       1521   3.0      1.0   95.00  \n", "4      NaN       1200   2.0      1.0   51.00  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data=pd.read_csv(\".\\\\Bengaluru_House_Data.csv\")\n", "data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "OZ-hExoDvqB0"}, "outputs": [], "source": ["#data cleaning begins"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9xLURDdzqZa3", "outputId": "df1cbde5-e82f-4b2b-e17d-954925ee567e"}, "outputs": [{"data": {"text/plain": ["array(['Super built-up  Area', 'Plot  Area', 'Built-up  Area',\n", "       'Carpet  Area'], dtype=object)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data.area_type.unique()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "Z5thfk1RYCcQ"}, "outputs": [], "source": ["data.drop(['availability','area_type','society','balcony'],axis=1,inplace=True) # he drops areatype insteaad of label encoding"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "TE5-U2EENjPH", "outputId": "9b1d2269-e668-4321-efa3-5caa146f9bc7"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10116\\3792323191.py:2: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  data['bath'].fillna(median,inplace=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>size</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Electronic City Phase II</td>\n", "      <td>2 BHK</td>\n", "      <td>1056</td>\n", "      <td>2.0</td>\n", "      <td>39.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> T<PERSON></td>\n", "      <td>4 Bedroom</td>\n", "      <td>2600</td>\n", "      <td>5.0</td>\n", "      <td>120.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Uttar<PERSON><PERSON>i</td>\n", "      <td>3 BHK</td>\n", "      <td>1440</td>\n", "      <td>2.0</td>\n", "      <td>62.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>3 BHK</td>\n", "      <td>1521</td>\n", "      <td>3.0</td>\n", "      <td>95.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ko<PERSON><PERSON></td>\n", "      <td>2 BHK</td>\n", "      <td>1200</td>\n", "      <td>2.0</td>\n", "      <td>51.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   location       size total_sqft  bath   price\n", "0  Electronic City Phase II      2 BHK       1056   2.0   39.07\n", "1          Chikka Tirupathi  4 Bedroom       2600   5.0  120.00\n", "2               Uttarahalli      3 BHK       1440   2.0   62.00\n", "3        Lingadheeranahalli      3 BHK       1521   3.0   95.00\n", "4                  Kothanur      2 BHK       1200   2.0   51.00"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["median=np.median(data['bath'].dropna())\n", "data['bath'].fillna(median,inplace=True)\n", "data.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 241}, "id": "77LIblK9Zqh8", "outputId": "373e7c2f-bf31-4784-86e6-6009f865b022"}, "outputs": [{"data": {"text/plain": ["location       1\n", "size          16\n", "total_sqft     0\n", "bath           0\n", "price          0\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "0KHHgBKba26A", "outputId": "443f6b32-c0c6-4d39-ffc3-f6c799499f41"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10116\\3818363600.py:4: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  data['bhk'].fillna(median2,inplace=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Electronic City Phase II</td>\n", "      <td>1056</td>\n", "      <td>2.0</td>\n", "      <td>39.07</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> T<PERSON></td>\n", "      <td>2600</td>\n", "      <td>5.0</td>\n", "      <td>120.00</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Uttar<PERSON><PERSON>i</td>\n", "      <td>1440</td>\n", "      <td>2.0</td>\n", "      <td>62.00</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1521</td>\n", "      <td>3.0</td>\n", "      <td>95.00</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ko<PERSON><PERSON></td>\n", "      <td>1200</td>\n", "      <td>2.0</td>\n", "      <td>51.00</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   location total_sqft  bath   price  bhk\n", "0  Electronic City Phase II       1056   2.0   39.07    2\n", "1          Chikka Tirupathi       2600   5.0  120.00    4\n", "2               Uttarahalli       1440   2.0   62.00    3\n", "3        Lingadheeranahalli       1521   3.0   95.00    3\n", "4                  Kothanur       1200   2.0   51.00    2"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data['bhk']=data['size'].astype(str).apply(lambda x: x.split(\" \")[0])\n", "data['bhk'] = pd.to_numeric(data['bhk'], errors='coerce')\n", "median2 = data['bhk'].dropna().median()\n", "data['bhk'].fillna(median2,inplace=True)\n", "data['bhk']=data['bhk'].astype('int')\n", "data.drop(['size'],axis=1,inplace=True)\n", "data.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RQfVflW-cMzC", "outputId": "3eeda1f0-1fcc-40e7-a377-08acba98d149"}, "outputs": [{"data": {"text/plain": ["array([ 2,  4,  3,  6,  1,  8,  7,  5, 11,  9, 27, 10, 19, 16, 43, 14, 12,\n", "       13, 18])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data['bhk'].unique()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 112}, "id": "T35uay5TcTqH", "outputId": "a8330952-4c9b-4b85-ee72-98690fb8b112"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1718</th>\n", "      <td>2Electronic City Phase II</td>\n", "      <td>8000</td>\n", "      <td>27.0</td>\n", "      <td>230.0</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4684</th>\n", "      <td>Munnekollal</td>\n", "      <td>2400</td>\n", "      <td>40.0</td>\n", "      <td>660.0</td>\n", "      <td>43</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       location total_sqft  bath  price  bhk\n", "1718  2Electronic City Phase II       8000  27.0  230.0   27\n", "4684                Munnekollal       2400  40.0  660.0   43"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data['bhk']>20]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81}, "id": "-KW23jpFhm5g", "outputId": "7d792328-b74a-4ef0-a13c-fcb12fecf3ed"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1718</th>\n", "      <td>2Electronic City Phase II</td>\n", "      <td>8000</td>\n", "      <td>27.0</td>\n", "      <td>230.0</td>\n", "      <td>27</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       location total_sqft  bath  price  bhk\n", "1718  2Electronic City Phase II       8000  27.0  230.0   27"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["data.drop(data[data['bath']==40].index,axis=0,inplace=True)  # its not possible to have 40 bathrooms with only 2400 sqft\n", "data[data['bhk']>20]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3RN22jxAXhJ9", "outputId": "340ff834-3f23-4a34-9391-6d104746221f"}, "outputs": [{"data": {"text/plain": ["array(['1056', '2600', '1440', ..., '1133 - 1384', '774', '4689'],\n", "      dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data['total_sqft'].unique()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "MzTf7VI0YorV"}, "outputs": [], "source": ["def is_float(string):\n", "  try:\n", "    float(string)\n", "  except:\n", "    return False\n", "  return True"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 676}, "id": "iyd460vgkjsM", "outputId": "204ea2d6-70a6-4cb9-fb05-cc74b8cc023e"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Yelahanka</td>\n", "      <td>2100 - 2850</td>\n", "      <td>4.0</td>\n", "      <td>186.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>3010 - 3410</td>\n", "      <td>2.0</td>\n", "      <td>192.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>Hennur Road</td>\n", "      <td>2957 - 3450</td>\n", "      <td>2.0</td>\n", "      <td>224.500</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>3067 - 8156</td>\n", "      <td>4.0</td>\n", "      <td>477.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>8th Phase JP Nagar</td>\n", "      <td>1042 - 1105</td>\n", "      <td>2.0</td>\n", "      <td>54.005</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>Sarjapur</td>\n", "      <td>1145 - 1340</td>\n", "      <td>2.0</td>\n", "      <td>43.490</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>KR Puram</td>\n", "      <td>1015 - 1540</td>\n", "      <td>2.0</td>\n", "      <td>56.800</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1520 - 1740</td>\n", "      <td>2.0</td>\n", "      <td>74.820</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>34.46Sq. Meter</td>\n", "      <td>1.0</td>\n", "      <td>18.500</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>549</th>\n", "      <td>Hennur Road</td>\n", "      <td>1195 - 1440</td>\n", "      <td>2.0</td>\n", "      <td>63.770</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>579</th>\n", "      <td>Sarjapur  Road</td>\n", "      <td>1200 - 2400</td>\n", "      <td>2.0</td>\n", "      <td>34.185</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>648</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>4125Perch</td>\n", "      <td>9.0</td>\n", "      <td>265.000</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>661</th>\n", "      <td>Yelahanka</td>\n", "      <td>1120 - 1145</td>\n", "      <td>2.0</td>\n", "      <td>48.130</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>669</th>\n", "      <td>JP Nagar</td>\n", "      <td>4400 - 6640</td>\n", "      <td>2.0</td>\n", "      <td>375.000</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>672</th>\n", "      <td>Bettahalsoor</td>\n", "      <td>3090 - 5002</td>\n", "      <td>4.0</td>\n", "      <td>445.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>702</th>\n", "      <td>JP Nagar</td>\n", "      <td>4400 - 6800</td>\n", "      <td>2.0</td>\n", "      <td>548.500</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>772</th>\n", "      <td>Banashankari Stage VI</td>\n", "      <td>1160 - 1195</td>\n", "      <td>2.0</td>\n", "      <td>59.935</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>775</th>\n", "      <td>Basavanagara</td>\n", "      <td>1000Sq. Meter</td>\n", "      <td>2.0</td>\n", "      <td>93.000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>801</th>\n", "      <td>JP Nagar</td>\n", "      <td>4000 - 5249</td>\n", "      <td>2.0</td>\n", "      <td>453.000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>850</th>\n", "      <td>Bannerghatta Road</td>\n", "      <td>1115 - 1130</td>\n", "      <td>2.0</td>\n", "      <td>58.935</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  location      total_sqft  bath    price  bhk\n", "30               Yelahanka     2100 - 2850   4.0  186.000    4\n", "56             Devanahalli     3010 - 3410   2.0  192.000    4\n", "81             Hennur Road     2957 - 3450   2.0  224.500    4\n", "122                 Hebbal     3067 - 8156   4.0  477.000    4\n", "137     8th Phase JP Nagar     1042 - 1105   2.0   54.005    2\n", "165               Sarjapur     1145 - 1340   2.0   43.490    2\n", "188               KR Puram     1015 - 1540   2.0   56.800    2\n", "224            Devanahalli     1520 - 1740   2.0   74.820    3\n", "410                Kengeri  34.46Sq. Meter   1.0   18.500    1\n", "549            Hennur Road     1195 - 1440   2.0   63.770    2\n", "579         Sarjapur  Road     1200 - 2400   2.0   34.185    3\n", "648                Arekere       4125<PERSON>erch   9.0  265.000    9\n", "661              Yelahanka     1120 - 1145   2.0   48.130    2\n", "669               JP Nagar     4400 - 6640   2.0  375.000    5\n", "672           Bettahalsoor     3090 - 5002   4.0  445.000    4\n", "702               JP Nagar     4400 - 6800   2.0  548.500    5\n", "772  Banashankari Stage VI     1160 - 1195   2.0   59.935    2\n", "775           Basavanagara   1000Sq. Meter   2.0   93.000    1\n", "801               JP Nagar     4000 - 5249   2.0  453.000    4\n", "850      Bannerghatta Road     1115 - 1130   2.0   58.935    2"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data[~data['total_sqft'].apply(is_float)].head(20)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "yODlIxw3lWMi"}, "outputs": [], "source": ["def convert_sqft_to_num(x):\n", "  tokens=x.split('-')\n", "  if len(tokens)==2:\n", "    return(float(tokens[0])+float(tokens[1]))/2\n", "  try:\n", "    return float(x)\n", "  except:\n", "    return None"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "N2EL4bDmOiwK", "outputId": "5cfa892f-98dc-41b3-f14d-04ac48e3f48a"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [location, total_sqft, bath, price, bhk]\n", "Index: []"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["data['total_sqft']=data['total_sqft'].apply(convert_sqft_to_num)\n", "data[~data['total_sqft'].apply(is_float)].head(20)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 241}, "id": "__RllKBerf76", "outputId": "7f94729c-35ca-4afa-c4da-0b8cd089a24b"}, "outputs": [{"data": {"text/plain": ["location      Basavanagara\n", "total_sqft             NaN\n", "bath                   2.0\n", "price                 93.0\n", "bhk                      1\n", "Name: 775, dtype: object"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["data.loc[775]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "F9xZGGEmrpJ5"}, "outputs": [{"data": {"text/plain": ["(13272, 5)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["data['total_sqft'] = pd.to_numeric(data['total_sqft'], errors='coerce')  # non number to nan\n", "data.dropna(subset=['total_sqft'],inplace=True)\n", "data=data.dropna()\n", "data.shape"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "K3oyXQmyuV6T"}, "outputs": [], "source": ["# Dimension reduction"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FSUJauOKPeFl", "outputId": "10c2508e-cc2c-4a5d-c608-1a4b2b3e8c38"}, "outputs": [{"data": {"text/plain": ["1299"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data['location'].unique())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "XT1CQ7OZwmpL"}, "outputs": [], "source": ["location_stats=data['location'].value_counts().sort_values(ascending=False)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "EWEf95WdxpqX"}, "outputs": [], "source": ["loc_less_than_10=location_stats[location_stats<=10]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TMv-jHe6Ja9n", "outputId": "f0c4380c-8ab4-4f3e-fb75-01e7043fdc47"}, "outputs": [{"data": {"text/plain": ["1059"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(loc_less_than_10)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JmEB2C62KR3_", "outputId": "af43c58f-4e49-4851-a411-5975da927b83"}, "outputs": [{"data": {"text/plain": ["1299"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data.location.unique())"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TKiVHoh0KoaK", "outputId": "f3582093-c108-4abd-d906-67be2215f340"}, "outputs": [{"data": {"text/plain": ["241"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["data.location=data.location.apply(lambda x: 'other' if x in loc_less_than_10 else x)\n", "len(data.location.unique())"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "q5G9jl6yLXAz", "outputId": "49dddd6b-24a0-42a3-f103-4b0818c8c3e4"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>other</td>\n", "      <td>1020.0</td>\n", "      <td>6.0</td>\n", "      <td>370.0</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>HSR Layout</td>\n", "      <td>600.0</td>\n", "      <td>9.0</td>\n", "      <td>200.0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1407.0</td>\n", "      <td>4.0</td>\n", "      <td>150.0</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>other</td>\n", "      <td>1350.0</td>\n", "      <td>7.0</td>\n", "      <td>85.0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>other</td>\n", "      <td>500.0</td>\n", "      <td>3.0</td>\n", "      <td>100.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         location  total_sqft  bath  price  bhk\n", "9           other      1020.0   6.0  370.0    6\n", "45     HSR Layout       600.0   9.0  200.0    8\n", "58  Murugeshpalya      1407.0   4.0  150.0    6\n", "68          other      1350.0   7.0   85.0    8\n", "70          other       500.0   3.0  100.0    3"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data.total_sqft/data.bhk < 300].head()  # got 300 by domain knowledge or friend\n", "#see 6 bhk in just 1020 all are outlier"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "nKpqtsftMmAC", "outputId": "4417dc64-2155-4696-dedc-94800880d956"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Electronic City Phase II</td>\n", "      <td>1056.0</td>\n", "      <td>2.0</td>\n", "      <td>39.07</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> T<PERSON></td>\n", "      <td>2600.0</td>\n", "      <td>5.0</td>\n", "      <td>120.00</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Uttar<PERSON><PERSON>i</td>\n", "      <td>1440.0</td>\n", "      <td>2.0</td>\n", "      <td>62.00</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1521.0</td>\n", "      <td>3.0</td>\n", "      <td>95.00</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ko<PERSON><PERSON></td>\n", "      <td>1200.0</td>\n", "      <td>2.0</td>\n", "      <td>51.00</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   location  total_sqft  bath   price  bhk\n", "0  Electronic City Phase II      1056.0   2.0   39.07    2\n", "1          Chikka Tirupathi      2600.0   5.0  120.00    4\n", "2               Uttarahalli      1440.0   2.0   62.00    3\n", "3        Lingadheeranahalli      1521.0   3.0   95.00    3\n", "4                  Kothanur      1200.0   2.0   51.00    2"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["data=data[~(data.total_sqft/data.bhk < 300)]\n", "data.head()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# feature engineering starts"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "inkqF2Invv4A", "outputId": "7ac590a0-a5ba-4f6f-b9be-106e3e02d944"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "      <th>price_per_sqft</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Electronic City Phase II</td>\n", "      <td>1056.0</td>\n", "      <td>2.0</td>\n", "      <td>39.07</td>\n", "      <td>2</td>\n", "      <td>3699.810606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> T<PERSON></td>\n", "      <td>2600.0</td>\n", "      <td>5.0</td>\n", "      <td>120.00</td>\n", "      <td>4</td>\n", "      <td>4615.384615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Uttar<PERSON><PERSON>i</td>\n", "      <td>1440.0</td>\n", "      <td>2.0</td>\n", "      <td>62.00</td>\n", "      <td>3</td>\n", "      <td>4305.555556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1521.0</td>\n", "      <td>3.0</td>\n", "      <td>95.00</td>\n", "      <td>3</td>\n", "      <td>6245.890861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ko<PERSON><PERSON></td>\n", "      <td>1200.0</td>\n", "      <td>2.0</td>\n", "      <td>51.00</td>\n", "      <td>2</td>\n", "      <td>4250.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   location  total_sqft  bath   price  bhk  price_per_sqft\n", "0  Electronic City Phase II      1056.0   2.0   39.07    2     3699.810606\n", "1          Chikka Tirupathi      2600.0   5.0  120.00    4     4615.384615\n", "2               Uttarahalli      1440.0   2.0   62.00    3     4305.555556\n", "3        Lingadheeranahalli      1521.0   3.0   95.00    3     6245.890861\n", "4                  Kothanur      1200.0   2.0   51.00    2     4250.000000"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["data['price_per_sqft']=data['price']*100000/data['total_sqft']\n", "data.head()"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "T65tz-422q9O", "outputId": "9d6ec4a4-ed25-44d0-9f71-fc190b4b3c57"}, "outputs": [{"data": {"text/plain": ["(10304, 6)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["def remove_pps_outliers(df):  # zscore 1 (68% retention), 2(95%), 3(99.7%)\n", "    df_out = pd.DataFrame()\n", "    for key, subdf in df.groupby('location'):  # in (m +- 1*std) datapoints lie withing 1 standard deviation away from mean\n", "        m = np.mean(subdf.price_per_sqft)\n", "        st = np.std(subdf.price_per_sqft)\n", "        reduced_df = subdf[(subdf.price_per_sqft>(m-st)) & (subdf.price_per_sqft<=(m+st))]  # mean + 3*std.. to this z score of 1 is choosen\n", "        df_out = pd.concat([df_out,reduced_df],ignore_index=True)\n", "    return df_out\n", "df2 = remove_pps_outliers(data)\n", "df2.shape"]}, {"cell_type": "code", "execution_count": 73, "metadata": {"id": "KPx2c6Wj4K7I"}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"id": "7S-Hy--04FhE"}, "outputs": [], "source": ["def plot_scatter_chart(df,location):\n", "    bhk2 = df[(df.location==location) & (df.bhk==2)]\n", "    bhk3 = df[(df.location==location) & (df.bhk==3)]\n", "    plt.figure(figsize = (5,4))\n", "    plt.scatter(bhk2.total_sqft,bhk2.price,color='blue',label='2 BHK', s=50)\n", "    plt.scatter(bhk3.total_sqft,bhk3.price,marker='+', color='green',label='3 BHK', s=50)\n", "    plt.xlabel(\"Total Square Feet Area\")\n", "    plt.ylabel(\"Price (Lakh Indian Rupees)\")\n", "    plt.title(location)\n", "    plt.legend()"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 410}, "id": "59nP1Y_46cX7", "outputId": "7297ea7c-57f8-4767-fe03-a3556d84350a"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_scatter_chart(df2,\"Hebbal\") #so wrongs are there"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"id": "xA5G4TR66w0I"}, "outputs": [], "source": ["def remove_bhk_outliers(df):\n", "    exclude_indices = np.array([])\n", "    for location, location_df in df.groupby('location'):\n", "        bhk_stats = {}\n", "        for bhk, bhk_df in location_df.groupby('bhk'):\n", "            bhk_stats[bhk] = {\n", "                'mean': np.mean(bhk_df['price_per_sqft']),\n", "                'std': np.std(bhk_df['price_per_sqft']), \n", "                'count': bhk_df.shape[0] \n", "                } \n", "        for bhk, bhk_df in location_df.groupby('bhk'):\n", "            stats = bhk_stats.get(bhk - 1)\n", "            if stats and stats['count'] > 5:\n", "                exclude_indices = np.append(exclude_indices, bhk_df[bhk_df['price_per_sqft'] < (stats['mean'])].index.values)\n", "    return df.drop(exclude_indices, axis = 'index')\n", "df3 = remove_bhk_outliers(df2)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 410}, "id": "ja5P7yM2_2CB", "outputId": "5cb57c4c-fbef-418e-cfea-1c2d946734b5"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAc8AAAGJCAYAAADsebhaAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy80BEi2AAAACXBIWXMAAA9hAAAPYQGoP6dpAABQ4ElEQVR4nO3deVxU5f4H8M+wyjrsW6KiImquoRm53iRxKddb6bVSM02DSkkzuy1aejHNtZtY3dLuryzTRC1XUkBRNCV3CZcwTFlUYke2eX5/zJ0jwzoHZpgBPu/X67yaOec55zyPc69fn10hhBAgIiIinZkZOwNERERNDYMnERGRTAyeREREMjF4EhERycTgSUREJBODJxERkUwMnkRERDIxeBIREcnE4ElERCQTgydRCzRkyBB069atznSxsbFQKBTYtm2b3t69aNEiKBQKvT2PyBgYPIlM2KZNm6BQKHDq1Klqr+saBIlIvxg8iYiIZGLwJCIikonBk6iZ+frrrxEYGAgbGxu4uLhg4sSJuHHjRrVpExMT8eijj8LGxgZ+fn7YsGFDtenKy8vx1ltvwcvLC3Z2dhg9enSVZx45cgRPPfUU2rRpA2tra/j6+mLu3LkoKirSexmJjM3C2Bkgorrl5OTgzp07Vc6XlpZqfV+6dCneeecdPP3003jxxRdx+/ZtfPzxxxg0aBBOnz4NJycnKe1ff/2FkSNH4umnn8akSZPw/fffY/bs2bCyssILL7xQ5bkKhQILFixAZmYm1qxZg+DgYJw5cwY2NjYAgK1bt6KwsBCzZ8+Gq6srfvnlF3z88cf4888/sXXrVv3/oRAZkyAik7Vx40YBoNbjwQcfFEIIcf36dWFubi6WLl2q9Yzz588LCwsLrfODBw8WAMTKlSulc8XFxaJXr17Cw8NDlJSUCCGEiImJEQDEAw88IHJzc6W033//vQAg1q5dK50rLCyskv+IiAihUCjEH3/8IZ177733BP/qoaaOzbZETcAnn3yC6OjoKkePHj2kNNu3b4dKpcLTTz+NO3fuSIeXlxf8/f0RExOj9UwLCwu89NJL0ncrKyu89NJLyMzMRGJiolba559/Hg4ODtL3v//97/D29saePXukc5oaKAAUFBTgzp07ePTRRyGEwOnTp/X2Z0FkCthsS9QEPPzww+jTp0+V887OzlJz7pUrVyCEgL+/f7XPsLS01Pru4+MDOzs7rXOdOnUCAFy/fh2PPPKIdL7yMxUKBTp27Ijr169L51JTU/Huu+9i165d+Ouvv7TS5+Tk1FFCoqaFwZOomVCpVFAoFNi7dy/Mzc2rXLe3tzfYu8vLy/H4448jKysLCxYsQOfOnWFnZ4ebN29i6tSpUKlUBns3kTEweBI1Ex06dIAQAn5+flINsja3bt1CQUGBVu3z8uXLAIB27dpppb1y5YrWdyEErl69KjUbnz9/HpcvX8ZXX32F559/XkoXHR1d3+IQmTT2eRI1E+PHj4e5uTkWL14MIYTWNSEE7t69q3WurKwMn376qfS9pKQEn376Kdzd3REYGKiV9r///S/y8vKk79u2bUNaWhpGjBgBAFJNt+J7hRBYu3atfgpHZGJY8yRqJjp06IAlS5Zg4cKFuH79OsaOHQsHBwekpKQgKioKM2fOxLx586T0Pj4++PDDD3H9+nV06tQJW7ZswZkzZ/DZZ59V6R91cXHBgAEDMG3aNGRkZGDNmjXo2LEjZsyYAQDo3LkzOnTogHnz5uHmzZtwdHTEDz/8UKXvk6i5YPAkakbefPNNdOrUCatXr8bixYsBAL6+vhg2bBhGjx6tldbZ2RlfffUVXnnlFXz++efw9PTEv//9bykgVvTWW2/h3LlziIiIQF5eHoYOHYr169fD1tYWgHow0o8//ohXX30VERERaNWqFcaNG4ewsDD07NnT8AUnamQKUbl9h4iIiGrFPk8iIiKZGDyJiIhkYvAkIiKSicGTiIhIJgZPIiIimRg8iYiIZOI8T6jXBL116xYcHBygUCiMnR0iIjICIQTy8vLg4+MDM7Pa65YMnlCv8enr62vsbBARkQm4ceMGWrduXWsaBk9A2qfwxo0bcHR0NHJuiIjIGHJzc+Hr66u1d21NGDwBqanW0dGRwZOIqIXTpfuOA4aIiIhkYvAkIiKSicGTiIhIJvZ56qi8vBylpaXGzkazZm5uDgsLC04XIiKTx+Cpg/z8fPz555/g7m2GZ2trC29vb1hZWRk7K0RENWLwrEN5eTn+/PNP2Nrawt3dnbUiAxFCoKSkBLdv30ZKSgr8/f3rnKRMRGQsDJ51KC0thRAC7u7usLGxMXZ2mjUbGxtYWlrijz/+QElJCVq1amXsLBFRE1BQUgD7CHsAQP7CfNhZ2Rn8nfynvY5Y42wcrG0SUVPAv6mIiIhkYrMtERE1OQUlBfc/l1b/GYDBmnAZPImIqMnR9HFW5vmRp9Z38Z5hZkmw2bYRFRUBGRnq/xpaREQE+vbtCwcHB3h4eGDs2LFITk6u9Z5FixZBoVBIh1KpxMCBAxEXF6eVrl27dlizZk219/fq1avG7wBw5MgRODk5Yc6cOZz6Q0RNFoNnI4iPB8aPB+ztAS8v9X/HjweOHjXcO+Pi4hAaGorjx48jOjoapaWlGDZsGAoKCmq978EHH0RaWhrS0tKQkJAAf39/PPHEE8jJyWlwnnbv3o2QkBCEh4djzZo1HIRFRPWWvzBfOjLmZUjnM+ZlaF0zFDbbGlhkJBAaCpibAyqV+pxKBfz4I7BjB7B+PTBrlv7fu2/fPq3vmzZtgoeHBxITEzFo0KAa77OwsICXlxcAwMvLC++//z42btyIy5cvo2/fvvXOz+bNmzFt2jSsXLkSYWFh9X4OERFQc1+mnaUdp6o0dfHx6sApBFBWpn2trEx9/uWXDVsD1dDUHF1cXHS+p7i4GBs3boSTkxMCAgLq/e5PPvkE06ZNw5dffsnASUTNAmueBrRqlbrGWTlwVmRuDqxeDfTvb7h8qFQqzJkzB/3790e3bt1qTXv+/HnY26s74gsLC+Hg4IAtW7ZU2ed0wYIFePvtt7XOlZSUoGvXrlrnkpKSEBYWhi+++AKTJ0/WQ2mIiIyPwdNAioqAnTvvN9XWpKwMiIpSpzfUAkahoaG4cOEC4uPj60wbEBCAXbt2AQDy8vKwZcsWPPXUU4iJiUGfPn2kdPPnz8fUqVO17l23bh0OHz6sda5169ZwcnLCihUrMGLECHh7eze8QEREFdhZ2RlsVG1N2GxrILm5dQdODZVKnd4QwsLC8NNPPyEmJgatW7euM72VlRU6duyIjh07onfv3li2bBkeeOCBKqNr3dzcpHSao7omYQcHB/z888+ws7PD3/72N6SlpemraERERsPgaSCOjoCuK82ZmanT65MQAmFhYYiKisKhQ4fg5+dX72eZm5ujqAHza5ydnfHzzz/D0dERQ4YMwa1bt+r9LCIiU8DgaSA2NsCYMYBFHQ3jFhbAuHH6b7INDQ3F119/jc2bN8PBwQHp6elIT0+vMwiWlZVJaa9cuYIlS5bg0qVLGDNmTIPy4+TkhOjoaDg7OzOAElGTx+BpQOHhQHl57WnKy4G5c/X/7sjISOTk5GDIkCHw9vaWji1bttR638WLF6W0vXr1wvfff4/IyEg8//zzDc6TUqnEgQMH4ObmhsGDB+PmzZsNfiYRkTEoBJd5QW5uLpRKJXJycqqMKr137x5SUlLg5+dXry2yNmxQT0epPOrWwkIdOA01z7OpauifNxFRfdUWCypjzdPAZs0CjhxRN+Fq+kDNzNTfjxxh4CQiaoo4VaUR9O+vPoqK1KNqHR0NNy2FiIgMj8GzEdnYMGgSETUHbLYlIiKSicGTiIhIJqMGz8jISPTo0QOOjo5wdHREUFAQ9u7dK12/d+8eQkND4erqCnt7e0yYMAEZGRlaz0hNTcWoUaNga2sLDw8PzJ8/H2W1LSZLRETUQEYNnq1bt8ayZcuQmJiIU6dO4bHHHsOYMWNw8eJFAMDcuXPx448/YuvWrYiLi8OtW7cwfvx46f7y8nKMGjUKJSUlOHbsGL766its2rQJ7777rrGKRERELYEwMc7OzuI///mPyM7OFpaWlmLr1q3StaSkJAFAJCQkCCGE2LNnjzAzMxPp6elSmsjISOHo6CiKi4t1fmdOTo4AIHJycqpcKyoqEpcuXRJFRUUNKBXpin/eRGQstcWCykymz7O8vBzfffcdCgoKEBQUhMTERJSWliI4OFhK07lzZ7Rp0wYJCQkAgISEBHTv3h2enp5SmpCQEOTm5kq11+oUFxcjNzdX6yAiItKV0YOnZv9Ia2trzJo1C1FRUejatSvS09NhZWUFJycnrfSenp5IT08HAKSnp2sFTs11zbWaREREQKlUSoevr69+C0VERM2a0YNnQEAAzpw5gxMnTmD27NmYMmUKLl26ZNB3Lly4EDk5OdJx48YNg77PGOoajFWdRYsWQaFQSIdSqcTAgQMRFxenla5du3ZVtijT3N+rV68avwPAkSNH4OTkhDlz5kBwZUgiaqKMHjw1+0cGBgYiIiICPXv2xNq1a+Hl5YWSkhJkZ2drpc/IyICXlxcAwMvLq8roW813TZrqWFtbS0FFcxhaQUkBFIsVUCxWoKCkwODvq2swVk0efPBBpKWlIS0tDQkJCfD398cTTzyBnJycBudp9+7dCAkJQXh4ONasWQOFQtHgZxIRGYPRg2dlKpUKxcXFCAwMhKWlJQ4ePChdS05ORmpqKoKCggAAQUFBOH/+PDIzM6U00dHRcHR0RNeuXRs976bkySefxMiRI+Hv749OnTph6dKlsLe3x/Hjx2u9z8LCAl5eXvDy8kLXrl3x/vvvIz8/H5cvX25QfjZv3ozx48dj+fLlHA1NRE2eUZfnW7hwIUaMGIE2bdogLy8PmzdvRmxsLPbv3w+lUonp06cjPDwcLi4ucHR0xCuvvIKgoCA88sgjAIBhw4aha9eueO6557B8+XKkp6fj7bffRmhoKKytrY1ZNJNSXl6OrVu3SoOxdFVcXIyNGzfCyckJAQEB9X7/J598gvDwcHz55ZeYPHlyvZ9DRGQqjBo8MzMz8fzzzyMtLQ1KpRI9evTA/v378fjjjwMAVq9eDTMzM0yYMAHFxcUICQnB+vXrpfvNzc3x008/Yfbs2QgKCoKdnR2mTJmC999/31hF0lKxebagtPrPAGBnZWeQ958/fx5BQUG4d+8e7O3tpcFYdd1jb28PACgsLISDgwO2bNlSpWl7wYIFePvtt7XOlZSUVHl+UlISwsLC8MUXXzBwElGzYdTg+cUXX9R6vVWrVvjkk0/wySef1Jimbdu22LNnj76zphf2EfbVnvf8SHuEsHjPMANnNIOxcnJysG3bNkyZMgVxcXG1BtCAgADs2rULAJCXl4ctW7bgqaeeQkxMDPr06SOlmz9/PqZOnap177p163D48GGtc61bt4aTkxNWrFiBESNGwNvbW38FJCIyEu6q0oxpBmMBQGBgIE6ePIm1a9fi008/1ekeAOjduzd27NiBNWvW4Ouvv5bOu7m5aaUDABcXlyrPc3BwwM8//4zHH38cf/vb3xATE8MASkRNHoOnAeUvzJc+F5QWSDXOjHkZsLM0TFNtbTSDseQyNzdHUVFRvd/r7OyMn3/+GcOGDcOQIUMQExMDHx+fej+PiMjYGDwNqKa+TDtLO4P1c2rUNhirNmVlZdICE5pm20uXLmHBggUNyo+TkxOio6MREhKCIUOGIDY2lgGUiJosBs9mqq7BWDW5ePGi1Kxqa2uLDh06IDIyEs8//3yD86RUKnHgwAEMHz4cgwcPRmxsLB544IEGP5eIqLEpBJd5QW5uLpRKJXJycqqMKr137x5SUlLg5+eHVq1a1fsdBSUF0gCi/IX5Bq95NlX6+vMmIpKrtlhQGWuejcTOys5go2qJiKhxmdwKQ0RERKaOwZOIiEgmBk8iIiKZGDx1xHFVjYN/zkTUFDB41sHc3ByAet1WMrzCwkIAgKWlpZFzQkRUM462rYOFhQVsbW1x+/ZtWFpawsyM/94wBCEECgsLkZmZCScnJ+kfLUREpojBsw4KhQLe3t5ISUnBH3/8YezsNHtOTk61bmRORGQKGDx1YGVlBX9/fzbdGpilpSVrnETUJDB46sjMzIwr3hAREQAOGCIiIpKNwZOIiEgmBk8iIiKZGDyJiIhkYvAkIiKSicGTiIhIJgZPIiIimRg8iYiIZJK9SEJKSgqOHDmCP/74A4WFhXB3d0fv3r0RFBTERQSIiKhF0Dl4fvPNN1i7di1OnToFT09P+Pj4wMbGBllZWbh27RpatWqFyZMnY8GCBWjbtq0h80xERGRUOgXP3r17w8rKClOnTsUPP/wAX19frevFxcVISEjAd999hz59+mD9+vV46qmnDJJhIiIiY1MIHXYf3r9/P0JCQnR64N27d3H9+nUEBgY2OHONJTc3F0qlEjk5OXB0dDR2doiIyAjkxAKdap66Bk4AcHV1haurq87piYiImhrZo21//fVXnD9/Xvq+c+dOjB07Fm+99Ra37CIiIr0qKCmAYrECisUKFJQUGDs7EtnB86WXXsLly5cBAL///jsmTpwIW1tbbN26FW+88YbeM0hERGRqZAfPy5cvo1evXgCArVu3YtCgQdi8eTM2bdqEH374Qd/5IyIiMjmy53kKIaBSqQAAP//8M5544gkAgK+vL+7cuaPf3BERUYtTsXm2oLT6zwBgZ2XXaHmqTHbw7NOnD5YsWYLg4GDExcUhMjISgHrxBE9PT71nkIiIWhb7CPtqz3t+pB1jxHt1ThYxGNnNtmvWrMGvv/6KsLAw/POf/0THjh0BANu2bcOjjz6q9wwSERGZGp3meeri3r17MDc3h6WlpT4e16g4z5OIyHRUbrbV1Dgz5mXAzvJ+U62+m231Ps+zsuzsbGzbtg3Xrl3D/Pnz4eLigkuXLsHT0xMPPPBAvTJNREQE1BwU7SztjNrPWZHs4Hnu3DkMHToUTk5OuH79OmbMmAEXFxds374dqamp+O9//2uIfBIREZkM2X2e4eHhmDZtGq5cuaK1i8rIkSNx+PBhvWaOiIjIFMmueZ48eRKffvpplfMPPPAA0tPT9ZIpIqKGKCgpkEZs5i/MN5mmPpLPzsrOqKNqayK75mltbY3c3Nwq5y9fvgx3d3e9ZIqIiMiUyQ6eo0ePxvvvv4/S0lIAgEKhQGpqKhYsWIAJEybIelZERAT69u0LBwcHeHh4YOzYsUhOTtZKM2TIECgUCq1j1qxZWmlSU1MxatQo2NrawsPDA/Pnz0dZWZncohEREelEdvBcuXIl8vPz4eHhgaKiIgwePBgdO3aEg4MDli5dKutZcXFxCA0NxfHjxxEdHY3S0lIMGzYMBQXaq0jMmDEDaWlp0rF8+XLpWnl5OUaNGoWSkhIcO3YMX331FTZt2oR3331XbtGIqAkrKCm4f1RalabiNSJ9qPc8z/j4eJw7dw75+fl46KGHEBwc3ODM3L59Gx4eHoiLi8OgQYMAqGuevXr1wpo1a6q9Z+/evXjiiSdw69YtaYWjDRs2YMGCBbh9+zasrKyq3FNcXIzi4mLpe25uLnx9fTnPk6gJUyxW6JTOFPvPyDTImecpu+apMWDAALzwwguYP3++XgInAOTk5AAAXFxctM5/8803cHNzQ7du3bBw4UIUFhZK1xISEtC9e3etpQFDQkKQm5uLixcvVvueiIgIKJVK6fD19dVL/omIqGWQPdpWpVJh6dKl2LBhAzIyMnD58mW0b98e77zzDtq1a4fp06fXKyMqlQpz5sxB//790a1bN+n8P/7xD7Rt2xY+Pj44d+4cFixYgOTkZGzfvh0AkJ6eXmVNXc33mkb/Lly4EOHh4dJ3Tc2TiJqu/IX50ufaVqUh0gfZwXPJkiX46quvsHz5csyYMUM6361bN6xZs6bewTM0NBQXLlxAfHy81vmZM2dKn7t37w5vb28MHToU165dQ4cOHer1Lmtra1hbW9frXiIyTU1hVRpqPmQ32/73v//FZ599hsmTJ8Pc3Fw637NnT/z222/1ykRYWBh++uknxMTEoHXr1rWm7devHwDg6tWrAAAvLy9kZGRopdF89/Lyqld+iIiIaiM7eN68eVPaSaUilUolTV/RlRACYWFhiIqKwqFDh+Dn51fnPWfOnAEAeHt7AwCCgoJw/vx5ZGZmSmmio6Ph6OiIrl27ysoPERGRLmQ323bt2hVHjhxB27Zttc5v27YNvXv3lvWs0NBQbN68GTt37oSDg4PUR6lUKmFjY4Nr165h8+bNGDlyJFxdXXHu3DnMnTsXgwYNQo8ePQAAw4YNQ9euXfHcc89h+fLlSE9Px9tvv43Q0FA2zRK1UKa6Kg01I0KmHTt2CKVSKZYtWyZsbW3FihUrxIsvviisrKzEgQMHZD0LQLXHxo0bhRBCpKamikGDBgkXFxdhbW0tOnbsKObPny9ycnK0nnP9+nUxYsQIYWNjI9zc3MTrr78uSktLdc5HTk6OAFDluUREZPryi/MFFkFgEUR+cX69nyMnFtRrnueRI0fw/vvv4+zZs9I8z3fffRfDhg3TZ1xvNNzPk4io6dLXWsYG389z4MCBiI6OrlfmiIiImrp6BU8AOHXqFJKSkgCo+0EDAwP1likiIqLaVFxqsfJyjBUZapqS7OD5559/YtKkSTh69CicnJwAANnZ2Xj00Ufx3Xff1TnVhIiIqKE0zbSVaRbH0DDUwDHZU1VefPFFlJaWIikpCVlZWcjKykJSUhJUKhVefPFFQ+SRiIjIpMgeMGRjY4Njx45VmZaSmJiIgQMHaq0721RwwBARUdNSudm2puUY5TTbGnTAkK+vb7WLIZSXl8PHx0fu44iIiGQz9nKMspttV6xYgVdeeQWnTp2Szp06dQqvvfYaPvroI71mjoiIyBTJbrZ1dnZGYWEhysrKYGGhrrhqPtvZaUf7rKws/eXUgNhsS0TUdDWJeZ41bUpNRERkDMZYjlF28JwyZYoh8kFERNRkyA6eqamptV5v06ZNvTNDRETUFMgOnu3atYNCoajxenl5eYMyRERkCvTVj0bNk+zgefr0aa3vpaWlOH36NFatWoWlS5fqLWNERESmSnbw7NmzZ5Vzffr0gY+PD1asWIHx48frJWNERESmqt4Lw1cWEBCAkydP6utxRESNztiLjVPTITt45ubman0XQiAtLQ2LFi2Cv7+/3jJGRNTYjL3YODUdsoOnk5NTlQFDQgj4+vri22+/1VvGiIiITJXs4BkTE6P13czMDO7u7ujYsaO04hARUVOUvzBf+lzbYuNEsqPd4MGDqz2flpaGpUuX4t///neDM0VEZAzGXmycmg5ZC8NfvHgR//73v/HZZ58hOzsbAHDnzh3MnTsX7du3r1IrJaKWpaCkAIrFCigWK7QG3xA1NzoHz127dqF379549dVXMWvWLPTp0wcxMTHo0qULkpKSEBUVhYsXLxoyr0RERCZB5+C5ZMkShIaGIjc3F6tWrcLvv/+OV199FXv27MG+ffswfPhwQ+aTiKhRaRYbF+8JNtlSFToHz+TkZISGhsLe3h6vvPIKzMzMsHr1avTt29eQ+SMiE1dQUnD/qDQ3suI1ouZE5wFDeXl50v5m5ubmsLGxQfv27Q2WMSJqGjg3kloiWaNt9+/fD6VSCQBQqVQ4ePAgLly4oJVm9OjR+ssdERGRCVIIIXT656CZWd0tvAqFoknuqiJn93Ai0lZ5Sbua5kay35BMnZxYoHPNU6VSNThjRNT8cG4ktUSy5nkSERERgycREZFsXIyWiPRGMzeSqLljzZOIiEgmBk8iIiKZ6t1sW1JSgszMzCqjcNu0adPgTBEREZky2cHzypUreOGFF3Ds2DGt80KIJjvPk4iISA7ZwXPq1KmwsLDATz/9BG9vbygUCkPki4iIyGTJDp5nzpxBYmIiOnfubIj8EBERmTzZA4a6du2KO3fuGCIvRERETYLs4Pnhhx/ijTfeQGxsLO7evYvc3Fytg4iIqLnTeWF4Dc0C8ZX7OpvygCEuDE9ERHJigeyaZ0xMDGJiYnDo0CGtQ3NOjoiICPTt2xcODg7w8PDA2LFjkZycrJXm3r17CA0NhaurK+zt7TFhwgRkZGRopUlNTcWoUaNga2sLDw8PzJ8/H2VlZXKLRkQtSEFJARSLFVAsVmjvDFPDeaKKZA8YGjx4sN5eHhcXh9DQUPTt2xdlZWV46623MGzYMFy6dAl2durdGObOnYvdu3dj69atUCqVCAsLw/jx43H06FEAQHl5OUaNGgUvLy8cO3YMaWlpeP7552FpaYl//etfessrERGRhuxmW43CwkKkpqaipKRE63yPHj3qnZnbt2/Dw8MDcXFxGDRoEHJycuDu7o7Nmzfj73//OwDgt99+Q5cuXZCQkIBHHnkEe/fuxRNPPIFbt27B01O9j+CGDRuwYMEC3L59G1ZWVnW+l822RC1PQUkB7CPsAQD5C/Ol7dNqOk/Nn0H289S4ffs2pk2bhr1791Z7vSF9njk5OQAAFxcXAEBiYiJKS0sRHBwspencuTPatGkjBc+EhAR0795dCpwAEBISgtmzZ+PixYvo3bt3lfcUFxejuLhY+s6BTkQtQ+WNuzUyCzJhV2JX5XzFzwA39Kb7ZAfPOXPmIDs7GydOnMCQIUMQFRWFjIwMLFmyBCtXrqx3RlQqFebMmYP+/fujW7duAID09HRYWVnByclJK62npyfS09OlNBUDp+a65lp1IiIisHjx4nrnlYgMx5A1P81zK2u/rn215z0/0v67hTvGkIbs4Hno0CHs3LkTffr0gZmZGdq2bYvHH38cjo6OiIiIwKhRo+qVkdDQUFy4cAHx8fH1ul+OhQsXIjw8XPqem5sLX19fg7+XiIiaB9nBs6CgAB4eHgAAZ2dn3L59G506dUL37t3x66+/1isTYWFh+Omnn3D48GG0bt1aOu/l5YWSkhJkZ2dr1T4zMjLg5eUlpfnll1+0nqcZjatJU5m1tTWsra3rlVciarryF+ZLn69nX0e3SHUr1y8v/oK2yrYA1E21mppoxrwM2FmyqZaqkj1VJSAgQJpO0rNnT3z66ae4efMmNmzYAG9vb1nPEkIgLCwMUVFROHToEPz8/LSuBwYGwtLSEgcPHpTOJScnIzU1FUFBQQCAoKAgnD9/HpmZmVKa6OhoODo6omvXrnKLR0RGUFBScP+o1OdY8Zo+31NYWlhnejtLO9hZ3T+INGSPtv36669RVlaGqVOnIjExEcOHD0dWVhasrKywadMmPPPMMzo/6+WXX8bmzZuxc+dOBAQESOeVSiVsbGwAALNnz8aePXuwadMmODo64pVXXgEAaVeX8vJy9OrVCz4+Pli+fDnS09Px3HPP4cUXX9R5qgpH2xIZl2KxbhtMNLTPUdf3aHC0bcsiJxbUe6qKRmFhIX777Te0adMGbm5usu6taUeWjRs3YurUqQDUiyS8/vrr+Pbbb1FcXIyQkBCsX79eq0n2jz/+wOzZsxEbGws7OztMmTIFy5Ytg4WFbq3SDJ5ExsXgSaagUYNnc8DgSc1BU56fWHkKiWaUa+U+x4aWKTP/fvfO7cLbUp/nhdkX4G7rLl3zsPdo0HuoadL7PM/w8HB88MEHsLOz0xqlWp1Vq1bpnlMiItQcFDV9jvpSU1B0t3VnwCRZdAqep0+fRmlpqfS5JtwYm4iIWgKdgmdMTEy1n4nIuGpaMYcr4xAZFvs8wT5Paroaa6ANUUug9z7P8ePH6/zy7du365yWiIioKdIpeCqVSumzEAJRUVFQKpXo06cPAPUC7tnZ2bKCLBE1XMUVc2obpUpE+qVT8Ny4caP0ecGCBXj66aexYcMGmJubA1AvVPDyyy+zyZOokTXWKFUi0iZ7eb4vv/wS8+bNkwInAJibmyM8PBxffvmlXjNHRERkimQHz7KyMvz2229Vzv/2229QqVR6yRQRtWwFJQVQLFZAsVihlzVtifRN9q4q06ZNw/Tp03Ht2jU8/PDDAIATJ05g2bJlmDZtmt4zSES6sbOy46haokYiO3h+9NFH8PLywsqVK5GWlgYA8Pb2xvz58/H666/rPYNERESmpkHzPHNzcwGgyQ8U4jxPIuNrrPVtiWqi93meNWGgISJ90SxqX5kmiGqwaZpMgewBQxkZGXjuuefg4+MDCwsLmJubax1EZPo4IIeoYWTXPKdOnYrU1FS888478Pb25mLwRKQXXPCBmhLZwTM+Ph5HjhxBr169DJAdImqpuOADNSWyg6evry+4ljxR08MdWIj0R3bwXLNmDd588018+umnaNeunQGyRESGwAE5RPojO3g+88wzKCwsRIcOHWBrawtLS0ut61lZWXrLHBG1TFzwgUxdvWqeRNT0cEAOkf7IDp5TpkwxRD6IyMA4IIdIf3QOnprVhOrChROIiKi50zl4Ojk51TqnUwgBhUKB8vJyvWSMiIjIVOkcPGNiYgyZDyJqoIKSAmlEbf7C/FqbYjkgh6hhdA6egwcPNmQ+iIiImgzZa9sSERG1dA3aVYWIjIurBhEZB4MnURPGVYOIjIPNtkRERDKx5knUhHHVICLjkB08CwoKsGzZMhw8eBCZmZlQqVRa13///Xe9ZY6IasdVg4iMQ3bwfPHFFxEXF4fnnnuOm2ETEVGLJDt47t27F7t370b//v0NkR8iIiKTJzt4Ojs7w8XFxRB5ITJZclbvMRauGkTUeGSPtv3ggw/w7rvvorCw0BD5ISIiMnk61Tx79+6t1bd59epVeHp6ol27dlU2w/7111/1m0MiIiITo1PwHDt2rIGzQWR6uHoPEdVEIYTQWyeJZluypiY3NxdKpRI5OTncj5QkisW6/W+Z/YxEzYOcWCC7z3PFihXVni8vL8c//vEPuY8jIh0VlBRAsVgBxWKFVq2YiBqf7NG2K1asgIuLC6ZPny6dKy8vx8SJE3HhwgW9Zo7ImLh6DxHVRHbNc/fu3Zg3bx62bdsGACgrK8NTTz2Fixcvyt4w+/Dhw3jyySfh4+MDhUKBHTt2aF2fOnUqFAqF1jF8+HCtNFlZWZg8eTIcHR3h5OSE6dOnIz8/H0QNZWdld/+oECw1q/doDiJqeWTXPPv27YsffvgBY8eOhZWVFb744gtcvXoVMTEx8PT0rPsBFRQUFKBnz5544YUXMH78+GrTDB8+HBs3bpS+W1tba12fPHky0tLSEB0djdLSUkybNg0zZ87E5s2b5RaNyORw0BKRaarXwvCPPfYY/vvf/2LChAno0qUL4uLi4ObmJvs5I0aMwIgRI2pNY21tDS8vr2qvJSUlYd++fTh58iT69OkDAPj4448xcuRIfPTRR/Dx8an2vuLiYhQXF0vfc3NzZeedqDFwyzEi06RT8KypVuju7g4nJyfMnDlTOrd9+3b95Ox/YmNj4eHhAWdnZzz22GNYsmQJXF1dAQAJCQlwcnKSAicABAcHw8zMDCdOnMC4ceOqfWZERAQWL16s13xS88bVe4ioIp2Cp1KprPZ8SEiIXjNT2fDhwzF+/Hj4+fnh2rVreOuttzBixAgkJCTA3Nwc6enp8PDw0LrHwsICLi4uSE9Pr/G5CxcuRHh4uPQ9NzcXvr6+BisHUX1x0BKRadIpeFbsc2xMEydOlD53794dPXr0QIcOHRAbG4uhQ4fW+7nW1tZV+k6JTBG3HCMyTbJH2xpT+/bt4ebmhqtXrwIAvLy8kJmZqZWmrKwMWVlZNfaTEhERNVS9Bgxt27YN33//PVJTU1FSUqJ1zZBr2/7555+4e/cuvL29AQBBQUHIzs5GYmIiAgMDAQCHDh2CSqVCv379DJYPIiJq2WTXPNetW4dp06bB09MTp0+fxsMPPwxXV1f8/vvvdY6crSw/Px9nzpzBmTNnAAApKSk4c+YMUlNTkZ+fj/nz5+P48eO4fv06Dh48iDFjxqBjx45SX2uXLl0wfPhwzJgxA7/88guOHj2KsLAwTJw4scaRtkTG1JBVgjSDlsR7gk22RMYmZAoICBCbN28WQghhb28vrl27JoQQ4p133hGhoaGynhUTEyMAVDmmTJkiCgsLxbBhw4S7u7uwtLQUbdu2FTNmzBDp6elaz7h7966YNGmSsLe3F46OjmLatGkiLy9PVj5ycnIEAJGTkyPrPiK58ovzBRZBYBFEfnG+sbNDRBXIiQWyF4a3tbVFUlIS2rZtCw8PD0RHR6Nnz564cuUKHnnkEdy9e1fvAd7QuDA8NZamsKk2UUtl0IXhvby8kJWVBQBo06YNjh8/DkDd5CozDhO1CAUlBfePSqsEVbxGRE2H7AFDjz32GHbt2oXevXtj2rRpmDt3LrZt24ZTp07VuJgCUUtRXc2SqwQRNT+yg+dnn30GlUoFAAgNDYWrqyuOHTuG0aNHY9asWXrPIBERkamRHTzNzMxgZna/tXfixImYOHEisrOzsXXrVu7pSc2KPvoouUoQUfOjt0US/vjjDzz33HP6ehxRk1FXn6ZGQ7c2y8zPlKa5ZOZn1pmeiAynXoskENF97NMkankYPIkq4R6aRFQXBk+iSuTWJOX0acrd2qxi8+ztwtvVfgYAD3vt3YWIyLB0Dp7r1q2r9frNmzcbnBmipqjGGqiA1mCj+vBc6Vnt+W6R3bRfxSZhokalc/BcvXp1nWnatGnToMwQmQKOjiWiuugcPFNSUgyZDyKTYUp7aGa8niF9vl14W6pxXph9Ae627o2aFyK6j32eRHqmqblWHGCUWZCJ9hHtAagDoq59lDWlc7d1Zz8nkRHpNM/zu+++0/mBN27cwNGjR+udIaKmzD7CXjoqDjBqv6699Lmmfkwiajp0Cp6RkZHo0qULli9fjqSkpCrXc3JysGfPHvzjH//AQw891CR3ViGqDvfQJKLq6Lwl2a5du/Dxxx/j0KFDsLOzg6enJ1q1aoW//voL6enpcHNzw9SpUzF37lx4ejatf1lzS7KmpagIyM0FHB0BGxvj5aO6pfsqzhHNLMiUapwXZl+Q+it/f/V3eNjdb3JlUCYyDXJigez9PO/cuYP4+Hj88ccfKCoqgpubG3r37o3evXtrrXnblDB4Ng3x8cCqVcDOnYBKBZiZAWPGAK+/DvTv3/j5qWvdW8VihU7P4TQTItMgJxbIHjDk5uaGsWPH1jdvRPUSGQmEhgLm5urACaj/++OPwI4dwPr1ADf1IaLGwtG2ZPLi49WBUwigrEz7mub7yy8D3bvrvwZauXapda2OpfsqNtX+8uIvePg/DwPgfFGi5oDBk0zeqlXqGmflwFmRuTmwerVhgmfFzzWNlK28dF/F+ZnVPbNinycRNT2y+zybI/Z5mq6iIsDe/n5TbW3MzID8fP0NIqpY6wTUAVFf00zYz0lkegza50nUmHJzdQucgDpdbm7Dg6emtlmx1gmom2a1apQKVFm6r6ZF5Ymoeal38CwpKUFKSgo6dOgACwvGYDIMR0d1jVLXmqc+Gg5qCoAVFzoAtPtANUv3cTk9opZB9tySwsJCTJ8+Hba2tnjwwQeRmpoKAHjllVewbNkyvWeQmqeiIiAjQ/3f2tjYqKej1PXvM3NzYNw4/dU668vD3kM6KgZLzXJ6moOImjbZwXPhwoU4e/YsYmNj0apVK+l8cHAwtmzZotfMUfMTHw+MH6/ux/TyUv93/HigthUdw8OB8vLan1teDty+XftzdMFmVyLShezguWPHDvz73//GgAEDoFDcnwT+4IMP4tq1a3rNHDUvkZHAoEHquZmV52oOHAhs2FD9fQMGqOdxKhS110CPHav9OfoUNyWOS/cRtWCyg+ft27fh4VG12amgoEArmBJVVNdcTSHUczVrqjnOmgUcOVL7VBRdnlMXXTetHvzV4DrTeNh7SMGVTbVEzYvs4NmnTx/s3r1b+q4JmP/5z38QFBSkv5xRs6KZq1kbzVzNmvTvD7i46Nb/qcPe7dViDZKIdCF7mOy//vUvjBgxApcuXUJZWRnWrl2LS5cu4dixY4iLizNEHskENGQx9qKi++vR1qasDIiKUqev7h36ek5dfnnxF/X7Sou0apjrhq/DI60fkf9AImp2ZNc8BwwYgDNnzqCsrAzdu3fHgQMH4OHhgYSEBAQGBhoij2RE9RngU1l95moa8jl16ereFV3du6KzW2et851cOknXurp3rd/DiahZ4ApD4ApDNam4GHvFfkoLC/XoVl0XY5e7StC1a4CnZ9VaY2OtNsTdUIhaJjmxQHbNc8+ePdi/f3+V8/v378fevXvlPo5MVEMH+FSk61xNhUIdGP38qq/hap5TV9+phYV+5nwSEdVEdvB88803UV7NpDshBN588029ZIqMTx8DfCrSZa5mxTaQ6qawxMcDd+/qNudz7lzd8lWd/IX50pEx7/6KQRnzMrSuEVHLJXvA0JUrV9C1a9X+ns6dO+Pq1at6yRQZlyEG5mjmar78ct07pFR8PqC+5+rVugN6xebkhuyuUtOIW80SfEREsmueSqUSv//+e5XzV69ehZ0d/2JpDgw1MEczV3PMGHWfpK7MzICVK6tvQq6of3/187kpNhEZmuzgOWbMGMyZM0drNaGrV6/i9ddfx+jRo/WaOTIOzWLsupC7GHv//sC2berBPCkp6n7OutTVTAuoa51ubvrfz5OIqDqyg+fy5cthZ2eHzp07w8/PD35+fujSpQtcXV3x0UcfGSKP1Mh0HeDTkIE5NjbqQ19jvSs2IesTl+AjourI7vNUKpU4duwYoqOjcfbsWdjY2KBHjx4YNGiQIfJHRhIeDuzYUXuahg7MuXCh/vdWR1/7eRIR1YXzPMF5njXZsKH6AT5y53lWp6gI+Pvfgb179Vf7bMjcTiIiObFAp5rnunXrMHPmTLRq1Qrr1q2rNe2rr76qe07JpM2aBXTvrp6OEhWlrtmZmambdOfOrV//Yny8etTsjh3ygmZdG2JbWKjzxcBJRI1Bp5qnn58fTp06BVdXV/j5+dX8MIWi2pG4NTl8+DBWrFiBxMREpKWlISoqCmPHjpWuCyHw3nvv4fPPP0d2djb69++PyMhI+Pv7S2mysrLwyiuv4Mcff4SZmRkmTJiAtWvXwt5e930ZWfOsW0PWttWoacUifYmP54AhIqo/va8wlJKSAldXV+lzTYecwAmotzHr2bMnPvnkk2qvL1++HOvWrcOGDRtw4sQJ2NnZISQkBPfu3ZPSTJ48GRcvXkR0dDR++uknHD58GDNnzpSVD6qbjU31S+bpqrYVi/QlKsowzyUiqkLIUFJSItq3by8uXbok5zadABBRUVHSd5VKJby8vMSKFSukc9nZ2cLa2lp8++23QgghLl26JACIkydPSmn27t0rFAqFuHnzps7vzsnJEQBETk5OwwtC1Ro3TggLCyHU4dMwh5WVsUtJRE2ZnFgga6qKpaWlVq3PkFJSUpCeno7g4GDpnFKpRL9+/ZCQkAAASEhIgJOTE/r06SOlCQ4OhpmZGU6cOFHjs4uLi5Gbm6t1kOFoViwyVI1To6QEuHnTsO8gIgLqMc8zNDQUH374IcoM/Ddheno6AMDT01PrvKenp3QtPT0dHh4eWtctLCzg4uIipalOREQElEqldPj6+uo596SR8lcKbJcroHpXAShTDP4+fU9/ISKqjux5nidPnsTBgwdx4MABdO/evcqSfNu3b9db5gxl4cKFCA8Pl77n5uYygDYTlf6tRURkELKDp5OTEyZMmGCIvGjx8vICAGRkZMDb21s6n5GRgV69eklpMjMzte4rKytDVlaWdH91rK2tYW1trf9MUxWN1MovCQho3PcRUcskO3hu3LjREPmows/PD15eXjh48KAULHNzc3HixAnMnj0bABAUFITs7GwkJiYiMDAQAHDo0CGoVCr069evUfJJVaX8lYKTp4D/fA5E/3IDmPa/C8ob2glzap72VB/+/pznSUSNQ+fgqVKpsGLFCuzatQslJSUYOnQo3nvvPdg04G+r/Px8rW3MUlJScObMGbi4uKBNmzaYM2cOlixZAn9/f/j5+eGdd96Bj4+PNBe0S5cuGD58OGbMmIENGzagtLQUYWFhmDhxInx8fOqdL2qY9uvaqz88+L9D44XB2gkX6Xdxq0b6dx0Rke7Bc+nSpVi0aBGCg4NhY2ODtWvXIjMzE19++WW9X37q1Cn87W9/k75r+iGnTJmCTZs24Y033kBBQQFmzpyJ7OxsDBgwAPv27UOrVq2ke7755huEhYVh6NCh0iIJda2CRIYTH2+c90ZGcoEEImo8Oq9t6+/vj3nz5uGll14CAPz8888YNWoUioqKYCZnc0YTxBWG9Gf8eGBHXAqEZhsx93PAs2PVn7/eAdzucT+xnpptDx4EHntML48iohZM72vbAkBqaipGjhwpfQ8ODoZCocCtW7fQunXr+ueWmg3NfE6hqhAUW925/znPR6/9nObmwNixDJxE1Ph0Dp5lZWVazaWAetGE0tJSvWeKmqbc3P8t3m5bYQS0bZb254rXCrXn6MqlUjVsSzQiovrSOXgKITB16lStKR737t3DrFmztOZ6NoV5nmQYUivHGzVMtpwyXPu7jgOGLCxq3hKN/ZxEZAw6B88pU6ZUOffss8/qNTNElR08qA6S+toSjYhIH7gZNjhgSF8yMgAvL2g3zdrdBkK7qT9/cgEocL9/rY5m28GDgdhY9Wd9bIlGRFQbgwwYIqqLo+P/Nq2uKSgWuMvq51y69P5nGxsGTSIyHU17jgmZFBsbdZOqRR3/JLOwAHr3Vn9WKKpP879FpYiITBKDJ+lVeLh6ME9tysuBjz9WL6jQs2f1aS5cAAYOBDZs0H8eiYgaisGT9GrAAPUAH4XifzXQQg/1qNpFAhYlHlAo7o+SFQI4e7b655SVqa+//DJw9GijFoGIqE4MnqR3s2YBR46om3A1i09pRskeOaK+DgCrVqkXOqiNuTmwerVh80tEJBdH24KjbQ2pplGyRUWAvf3/FlWog5kZkJ/PAUNEZFgcbUsmo6ZRstJqRDpQqdTpGTyJyFSw2ZaMQjOtRRdmZhVWLyIiMgEMnmQUcqa1jBvHWicRmRYGTzIaXae1cPF3IjI1DJ5kNFWmtVRgYQGtaS1ERKaEwZOMStdpLUREpoSjbcno+vdXH1z8nYiaCgZPks1QQY6LvxNRU8FmW9JZfDwwfrx6cQMvL/V/x4/n8nlE1PIweJJOIiOBQYOAH3+8v7iBSqX+zgXciailYfCkOsXHA6Gh6oXay8q0r3EBdyJqiRg8qU5cwJ2ISBuDJ9WqqAjYubNqjbOysjIgKkqdnoiouWPwpFrVZwF3IqLmjsGzGSsqAjIy5NUGK9/DBdyJiKpi8GyG6jOlpKZ7fv2VC7gTEVXG4NnM1GdKSWSk+tqOHdr3REWp159t3163BdynTGGfJxG1DAyezUh9ppTEx6vPAerr1Vm5Epg4sfoF3CuOwh09mgsnEFHLwODZjNRnSsk//6nbs7/9FnjmGcDDQ/t8ebm6r1MTeLlwAhG1BAohaqpvtBy5ublQKpXIycmBYxMd8VJUpK716TIy1swMyM9Xf7a1lfceMzPdR98qFOqdUbilGBE1BXJiAWuezUR9ppRkZMh/j67vALhwAhE1XwyeJqI+00oqkjulxNISuH27fu/SFRdOIKLmisHTyPS1U4mNjW5TSszN1e9xdwcefrj++dYVF04gouaIwdOI9L1TSXi4blNKMjLkNb82BBdOIKLmiMHTSAyxU8mAAcD69XVPKakrwOoLF04gouaKwdNIDLVTyaxZ6hGuY8bc7wM1MwM8Pet+n76VlwNz5zbuO4mIGgOnqqDxp6rUZ1pJfWpvRUXq/kZLS3UfZ2M11VpYqAPn+vXqYE5E1BRwqoqJ09dOJXWN0LWxUdc4S0sbt49zzBh17ZeBk4iaK5MOnosWLYJCodA6OnfuLF2/d+8eQkND4erqCnt7e0yYMAEZ9Zm82MjkTCtRKKoOuJE7QlfO++rr4EEgPV1dS962jQsjEFHzZtLBEwAefPBBpKWlSUd8fLx0be7cufjxxx+xdetWxMXF4datWxg/frwRc6sbzbQSXfoghVDvbKJRnxG6uk5jkcvCQh3cIyOBxx5T13I5OIiIWgKTD54WFhbw8vKSDjc3NwBATk4OvvjiC6xatQqPPfYYAgMDsXHjRhw7dgzHjx83cq7rpsu0EkB70FBDRujq+j5dsXmWiFoykw+eV65cgY+PD9q3b4/JkycjNTUVAJCYmIjS0lIEBwdLaTt37ow2bdogISGh1mcWFxcjNzdX62hsgYHqWltdysvvr9LTkBG6tU1j0ZVCweZZIiLAxINnv379sGnTJuzbtw+RkZFISUnBwIEDkZeXh/T0dFhZWcHJyUnrHk9PT6Snp9f63IiICCiVSunw9fU1YCmql5tb8xZglalU6oFBO3dWrXFWVtuSeNVNY9EE8NqCsqZ5dv16Ns8SEQGAnnvB9GvEiBHS5x49eqBfv35o27Ytvv/+e9g04G/vhQsXIjw8XPqem5vb6AFUM4hH1+kqgPwRutX9EfXvrz4001gcHdV9qqtXq4Nu5XdommfnzmUtk4hIw6SDZ2VOTk7o1KkTrl69iscffxwlJSXIzs7Wqn1mZGTAy8ur1udYW1vD2tpar3mrGIx0ieuaQTw//lh7bdLCQp3O01NesK1ruqqNzf18VhdQAXnlISJqSUy62bay/Px8XLt2Dd7e3ggMDISlpSUOHjwoXU9OTkZqaiqCgoIaLU8NWdhd17Vo587VfcRsQ5bE08wL1QRWNs8SEVXPpIPnvHnzEBcXh+vXr+PYsWMYN24czM3NMWnSJCiVSkyfPh3h4eGIiYlBYmIipk2bhqCgIDzyyCONkr+GLuxe2yCeiv2MmuZSOcGWiIgMx6SD559//olJkyYhICAATz/9NFxdXXH8+HG4u7sDAFavXo0nnngCEyZMwKBBg+Dl5YXt27c3St70tbB7TWvRVjcNRG6wJSIiw+Datqjf2rbjx+veX7ltm2750LXf9OhR7QE+ZmbqploO6iEiqj85sYDBE/KDZ2Mt7K5LPjioh4hIP+TEgiY12tZU1Gdhd0MEt4ojZomIqPGYdJ+nqZKz0Lou00aIiKhpYfCsh8aYNkJERKaLwbOeOG2EiKjlYvCsJ04bISJquRg8G0DOHE0iImo+ONq2gapbF5Z9nEREzRuDp55w2ggRUcvBZlsiIiKZGDyJiIhkYvAkIiKSicGTiIhIJgZPIiIimTjaFoBmY5nc3Fwj54SIiIxFEwN02WyMwRNAXl4eAMDX19fIOSEiImPLy8uDUqmsNQ338wSgUqlw69YtODg4QKFQGDs7epebmwtfX1/cuHFD582+m4OWWm6g5Zad5Wa5G0IIgby8PPj4+MCsjq2zWPMEYGZmhtatWxs7Gwbn6OjYov6PpdFSyw203LKz3C2LPstdV41TgwOGiIiIZGLwJCIikonBswWwtrbGe++9B2tra2NnpVG11HIDLbfsLDfL3Vg4YIiIiEgm1jyJiIhkYvAkIiKSicGTiIhIJgZPIiIimRg8m4jDhw/jySefhI+PDxQKBXbs2KF1XQiBd999F97e3rCxsUFwcDCuXLmilSYrKwuTJ0+Go6MjnJycMH36dOTn52ulOXfuHAYOHIhWrVrB19cXy5cvN3TRalVXuadOnQqFQqF1DB8+XCtNUyx3REQE+vbtCwcHB3h4eGDs2LFITk7WSnPv3j2EhobC1dUV9vb2mDBhAjIyMrTSpKamYtSoUbC1tYWHhwfmz5+PsrIyrTSxsbF46KGHYG1tjY4dO2LTpk2GLl6NdCn3kCFDqvzms2bN0krT1ModGRmJHj16SJP9g4KCsHfvXul6c/ytNeoqu8n+3oKahD179oh//vOfYvv27QKAiIqK0rq+bNkyoVQqxY4dO8TZs2fF6NGjhZ+fnygqKpLSDB8+XPTs2VMcP35cHDlyRHTs2FFMmjRJup6TkyM8PT3F5MmTxYULF8S3334rbGxsxKefftpYxayirnJPmTJFDB8+XKSlpUlHVlaWVpqmWO6QkBCxceNGceHCBXHmzBkxcuRI0aZNG5Gfny+lmTVrlvD19RUHDx4Up06dEo888oh49NFHpetlZWWiW7duIjg4WJw+fVrs2bNHuLm5iYULF0ppfv/9d2FrayvCw8PFpUuXxMcffyzMzc3Fvn37GrW8GrqUe/DgwWLGjBlav3lOTo50vSmWe9euXWL37t3i8uXLIjk5Wbz11lvC0tJSXLhwQQjRPH9rjbrKbqq/N4NnE1Q5iKhUKuHl5SVWrFghncvOzhbW1tbi22+/FUIIcenSJQFAnDx5Ukqzd+9eoVAoxM2bN4UQQqxfv144OzuL4uJiKc2CBQtEQECAgUukm5qC55gxY2q8pzmUWwghMjMzBQARFxcnhFD/vpaWlmLr1q1SmqSkJAFAJCQkCCHU//AwMzMT6enpUprIyEjh6OgolfWNN94QDz74oNa7nnnmGRESEmLoIumkcrmFUP9l+tprr9V4T3MotxBCODs7i//85z8t5reuSFN2IUz392azbTOQkpKC9PR0BAcHS+eUSiX69euHhIQEAEBCQgKcnJzQp08fKU1wcDDMzMxw4sQJKc2gQYNgZWUlpQkJCUFycjL++uuvRiqNfLGxsfDw8EBAQABmz56Nu3fvSteaS7lzcnIAAC4uLgCAxMRElJaWav3mnTt3Rps2bbR+8+7du8PT01NKExISgtzcXFy8eFFKU/EZmjSaZxhb5XJrfPPNN3Bzc0O3bt2wcOFCFBYWSteaernLy8vx3XffoaCgAEFBQS3mtwaqll3DFH9vLgzfDKSnpwOA1v94NN8119LT0+Hh4aF13cLCAi4uLlpp/Pz8qjxDc83Z2dkg+W+I4cOHY/z48fDz88O1a9fw1ltvYcSIEUhISIC5uXmzKLdKpcKcOXPQv39/dOvWTcqXlZUVnJyctNJW/s2r+9+E5lptaXJzc1FUVAQbGxtDFEkn1ZUbAP7xj3+gbdu28PHxwblz57BgwQIkJydj+/btAJpuuc+fP4+goCDcu3cP9vb2iIqKQteuXXHmzJlm/1vXVHbAdH9vBk9q0iZOnCh97t69O3r06IEOHTogNjYWQ4cONWLO9Cc0NBQXLlxAfHy8sbPSqGoq98yZM6XP3bt3h7e3N4YOHYpr166hQ4cOjZ1NvQkICMCZM2eQk5ODbdu2YcqUKYiLizN2thpFTWXv2rWryf7ebLZtBry8vACgyui7jIwM6ZqXlxcyMzO1rpeVlSErK0srTXXPqPgOU9e+fXu4ubnh6tWrAJp+ucPCwvDTTz8hJiZGa9s8Ly8vlJSUIDs7Wyt95d+8rnLVlMbR0dGoNZGayl2dfv36AYDWb94Uy21lZYWOHTsiMDAQERER6NmzJ9auXdvsf2ug5rJXx1R+bwbPZsDPzw9eXl44ePCgdC43NxcnTpyQ+g2CgoKQnZ2NxMREKc2hQ4egUqmk/zEGBQXh8OHDKC0tldJER0cjICDA6E2Xuvrzzz9x9+5deHt7A2i65RZCICwsDFFRUTh06FCVZuXAwEBYWlpq/ebJyclITU3V+s3Pnz+v9Y+H6OhoODo6Sk1iQUFBWs/QpKnY39SY6ip3dc6cOQMAWr95Uyt3dVQqFYqLi5vtb10bTdmrYzK/d72HGlGjysvLE6dPnxanT58WAMSqVavE6dOnxR9//CGEUE9VcXJyEjt37hTnzp0TY8aMqXaqSu/evcWJEydEfHy88Pf315qykZ2dLTw9PcVzzz0nLly4IL777jtha2tr1CkbtZU7Ly9PzJs3TyQkJIiUlBTx888/i4ceekj4+/uLe/fuSc9oiuWePXu2UCqVIjY2VmuIfmFhoZRm1qxZok2bNuLQoUPi1KlTIigoSAQFBUnXNUP4hw0bJs6cOSP27dsn3N3dqx3CP3/+fJGUlCQ++eQTo05fqKvcV69eFe+//744deqUSElJETt37hTt27cXgwYNkp7RFMv95ptviri4OJGSkiLOnTsn3nzzTaFQKMSBAweEEM3zt9aoreym/HszeDYRMTExAkCVY8qUKUII9XSVd955R3h6egpra2sxdOhQkZycrPWMu3fvikmTJgl7e3vh6Ogopk2bJvLy8rTSnD17VgwYMEBYW1uLBx54QCxbtqyxilit2spdWFgohg0bJtzd3YWlpaVo27atmDFjhtaQdSGaZrmrKzMAsXHjRilNUVGRePnll4Wzs7OwtbUV48aNE2lpaVrPuX79uhgxYoSwsbERbm5u4vXXXxelpaVaaWJiYkSvXr2ElZWVaN++vdY7Gltd5U5NTRWDBg0SLi4uwtraWnTs2FHMnz9fa96fEE2v3C+88IJo27atsLKyEu7u7mLo0KFS4BSief7WGrWV3ZR/b25JRkREJBP7PImIiGRi8CQiIpKJwZOIiEgmBk8iIiKZGDyJiIhkYvAkIiKSicGTiIhIJgZPIiIimRg8ifREoVBgx44dxs4GETUCBk9qdhQKRa3HokWLarz3+vXrUCgU0uLT+nT79m3Mnj0bbdq0gbW1Nby8vBASEoKjR4/q/V2NJTY2tto/47ffflsvz6/P7xESEgJzc3OcPHlSL3kgqg7386RmJy0tTfq8ZcsWvPvuu0hOTpbO2dvbGyNbmDBhAkpKSvDVV1+hffv2yMjIwMGDB3H37l2j5KeikpISWFlZ1fv+5ORkODo6St+N9WecmpqKY8eOISwsDF9++SX69u1ba/qGlptasAatjEtk4jZu3CiUSqX0vby8XCxevFg88MADwsrKSvTs2VPs3btXuo5KC5IPHjxYCCHEL7/8IoKDg4Wrq6twdHQUgwYNEomJiVrvAiCioqKqzcdff/0lAIjY2Nha83v58mUxcOBAYW1tLbp06SIOHDig9VzNQvl//fWXdI9mx5mUlBQhhBB37twREydOFD4+PsLGxkZ069ZNbN68Wes9gwcPFqGhoeK1114Trq6uYsiQIUIIIc6fPy+GDx8u7OzshIeHh3j22WfF7du3a8xvdfmpKDU1VTz11FNCqVQKZ2dnMXr0aCmfGp9//rno3LmzsLa2FgEBAeKTTz6RrtX0e9Rk0aJFYuLEiSIpKUkolUqtXWgaUu69e/eK/v37C6VSKVxcXMSoUaPE1atXa80LNW9stqUWZe3atVi5ciU++ugjnDt3DiEhIRg9ejSuXLkCAPjll18AAD///DPS0tKwfft2AEBeXh6mTJmC+Ph4HD9+HP7+/hg5ciTy8vJ0eq+9vT3s7e2xY8eOGvcpVKlUGD9+PKysrHDixAls2LABCxYskF3Ge/fuITAwELt378aFCxcwc+ZMPPfcc1LZNL766itYWVnh6NGj2LBhA7Kzs/HYY4+hd+/eOHXqFPbt24eMjAw8/fTTsvMAAKWlpQgJCYGDgwOOHDmCo0ePwt7eHsOHD0dJSQkA4JtvvsG7776LpUuXIikpCf/617/wzjvv4KuvvgJQ8+9RHSEENm7ciGeffRadO3dGx44dsW3btirp6lPugoIChIeH49SpUzh48CDMzMwwbtw4qFSqev3ZUDNg7OhNZEiVa54+Pj5i6dKlWmn69u0rXn75ZSGEECkpKQKAOH36dK3PLS8vFw4ODuLHH3+UzqGWmqcQQmzbtk04OzuLVq1aiUcffVQsXLhQnD17Vrq+f/9+YWFhIW7evCmd27t3r+yaZ3VGjRolXn/9den74MGDRe/evbXSfPDBB2LYsGFa527cuCEAVNneTkOTHzs7O63jzp074v/+7/9EQECAUKlUUvri4mJhY2Mj9u/fL4QQokOHDlVqxR988IG0V6Wuv4cQQhw4cEC4u7tLW1GtXr26Sk1VX+W+ffu2ACDOnz9fZ76oeWLNk1qM3Nxc3Lp1C/3799c6379/fyQlJdV6b0ZGBmbMmAF/f38olUo4OjoiPz8fqampOr9/woQJuHXrFnbt2oXhw4cjNjYWDz30EDZt2gQASEpKgq+vL3x8fKR76rPTfXl5OT744AN0794dLi4usLe3x/79+6vkNTAwUOv72bNnERMTI9WS7e3t0blzZwDAtWvXan3nkSNHcObMGelwdnbG2bNncfXqVTg4OEjPc3Fxwb1793Dt2jUUFBTg2rVrmD59utY7lyxZUuf7qvPll1/imWeegYWFeijHpEmTcPTo0SrPqk+5r1y5gkmTJqF9+/ZwdHREu3btAEDW70/NCwcMEelgypQpuHv3LtauXYu2bdvC2toaQUFBUvOjrlq1aoXHH38cjz/+ON555x28+OKLeO+99zB16lSd7jczU/97V1TYhre0tFQrzYoVK7B27VqsWbMG3bt3h52dHebMmVMlr3Z2dlrf8/Pz8eSTT+LDDz+s8l5vb+9a8+Xn5wcnJ6cqzwsMDMQ333xTJb27uzvy8/MBAJ9//jn69eundd3c3LzW91WWlZWFqKgolJaWIjIyUjpfXl6OL7/8EkuXLpXO1afcTz75JNq2bYvPP/8cPj4+UKlU6Natm+zfn5oPBk9qMRwdHeHj44OjR49i8ODB0vmjR4/i4YcfBgBp5GV5ebnWvUePHsX69esxcuRIAMCNGzdw586dBuepa9eu0tzQLl264MaNG0hLS5P+0j5+/LhWend3dwDqEcXOzs4AUGUax9GjRzFmzBg8++yzANR9qZcvX0bXrl1rzctDDz2EH374Ae3atZNqbw3x0EMPYcuWLfDw8NAaiauhVCrh4+OD33//HZMnT672GTX9HpV98803aN26dZV5tgcOHMDKlSvx/vvv1xiQ6yr33bt3kZycjM8//xwDBw4EAMTHx9eaH2r+2GxLLcr8+fPx4YcfYsuWLUhOTsabb76JM2fO4LXXXgMAeHh4wMbGRho0kpOTAwDw9/fH//3f/yEpKQknTpzA5MmTYWNjo/N77969i8ceewxff/01zp07h5SUFGzduhXLly/HmDFjAADBwcHo1KkTpkyZgrNnz+LIkSP45z//qfWcjh07wtfXF4sWLcKVK1ewe/durFy5UiuNv78/oqOjcezYMSQlJeGll15CRkZGnXkMDQ1FVlYWJk2ahJMnT+LatWvYv38/pk2bVmfwqs7kyZPh5uaGMWPG4MiRI0hJSUFsbCxeffVV/PnnnwCAxYsXIyIiAuvWrcPly5dx/vx5bNy4EatWrQJQ8+9R2RdffIG///3v6Natm9Yxffp03LlzB/v27at3uZ2dneHq6orPPvsMV69exaFDhxAeHi77z4OaGWN3uhIZUnVTVRYtWiQeeOABYWlpWWWqihDqqRO+vr7CzMxMGnDy66+/ij59+ohWrVoJf39/sXXrVtG2bVuxevVq6T7UMmDo3r174s033xQPPfSQUCqVwtbWVgQEBIi3335bazpFcnKyGDBggLCyshKdOnUS+/btq/Lc+Ph40b17d9GqVSsxcOBAsXXrVq0BQ3fv3hVjxowR9vb2wsPDQ7z99tvi+eefF2PGjJGeMXjwYPHaa69Vyefly5fFuHHjhJOTk7CxsRGdO3cWc+bM0Rr0U1FdU1XS0tLE888/L9zc3IS1tbVo3769mDFjhsjJyZHSfPPNN6JXr17CyspKODs7i0GDBont27fX+ntUdOrUKQFA/PLLL9XmYcSIEWLcuHENKnd0dLTo0qWLsLa2Fj169BCxsbF1DhCj5k0hRIXOEyIyOQqFAlFRURg7dqyxs0JE/8NmWyIiIpkYPImIiGTiaFsiE8eeFSLTw5onERGRTAyeREREMjF4EhERycTgSUREJBODJxERkUwMnkRERDIxeBIREcnE4ElERCTT/wNbsG0qqrYyVgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 500x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_scatter_chart(df3,\"Hebbal\") # yayayy chuu mantrrr hogae"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vNXSpilRATUR", "outputId": "7a6498a3-73ad-4ce8-e5f4-6064b4e18c31"}, "outputs": [{"data": {"text/plain": ["(7361, 6)"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["df3.reset_index(drop=True)\n", "df3.shape\n"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xcx0AYLJawH8", "outputId": "fa4c2d56-d1a8-4cac-e5e6-e7315dcbd777"}, "outputs": [{"data": {"text/plain": ["array([ 2,  4,  3,  1,  8,  6,  5,  7, 11,  9, 10, 16, 13])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["data['bhk'].unique()"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "nHhz2JpjuorZ", "outputId": "708254af-8497-4de9-977f-f7c1c824c075"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "      <th>price_per_sqft</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [location, total_sqft, bath, price, bhk, price_per_sqft]\n", "Index: []"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["data[pd.isnull(data.total_sqft)]"]}, {"cell_type": "code", "execution_count": 81, "metadata": {"id": "AjMxFq67K6ss"}, "outputs": [], "source": ["# outlier detection and removal\n"]}, {"cell_type": "code", "execution_count": 82, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 335}, "id": "TeQfsWOfNG1m", "outputId": "09fe9674-df71-43d9-a5c1-6ce9459e181e"}, "outputs": [{"data": {"text/plain": ["count     12529.000000\n", "mean       6304.053504\n", "std        4162.395819\n", "min         267.829813\n", "25%        4210.526316\n", "50%        5294.117647\n", "75%        6916.666667\n", "max      176470.588235\n", "Name: price_per_sqft, dtype: float64"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["data.price_per_sqft.describe()  # max too away from 75%"]}, {"cell_type": "code", "execution_count": 83, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "5Wgve2UDQjXm", "outputId": "70534dda-72d3-4e8b-d0b6-490115860868"}, "outputs": [{"data": {"text/plain": ["'q1=4210.5\\nq3=6916.7\\niqr=q3-q1\\nx=q1-iqr*1.5\\ny=q3+iqr*1.5\\ndata=data[(data.price_per_sqft <=y) & ( data.price_per_sqft >=x)]\\ndata.price_per_sqft.describe()'"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["'''q1=4210.5\n", "q3=6916.7\n", "iqr=q3-q1\n", "x=q1-iqr*1.5\n", "y=q3+iqr*1.5\n", "data=data[(data.price_per_sqft <=y) & ( data.price_per_sqft >=x)]\n", "data.price_per_sqft.describe()'''"]}, {"cell_type": "code", "execution_count": 84, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8nMEIBkVpB0m", "outputId": "ba906eca-de50-4a1e-deb0-ba4bbff87041"}, "outputs": [{"data": {"text/plain": ["(12529, 6)"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": 85, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 466}, "id": "kkF9YDSxA0BN", "outputId": "9cd2f0e4-f24e-4627-9fe5-9d83925e76ec"}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Count')"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(df3.bath,rwidth=0.8)\n", "plt.xlabel(\"Number of bathrooms\")\n", "plt.ylabel(\"Count\")"]}, {"cell_type": "code", "execution_count": 86, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "3OLZ1IoWA328", "outputId": "e07072a7-a8ba-4950-b552-4033990a2db9"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "      <th>price_per_sqft</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5307</th>\n", "      <td>Neeladri Nagar</td>\n", "      <td>4000.0</td>\n", "      <td>12.0</td>\n", "      <td>160.0</td>\n", "      <td>10</td>\n", "      <td>4000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8534</th>\n", "      <td>other</td>\n", "      <td>12000.0</td>\n", "      <td>12.0</td>\n", "      <td>525.0</td>\n", "      <td>10</td>\n", "      <td>4375.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8623</th>\n", "      <td>other</td>\n", "      <td>10000.0</td>\n", "      <td>16.0</td>\n", "      <td>550.0</td>\n", "      <td>16</td>\n", "      <td>5500.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9363</th>\n", "      <td>other</td>\n", "      <td>6000.0</td>\n", "      <td>12.0</td>\n", "      <td>150.0</td>\n", "      <td>11</td>\n", "      <td>2500.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9696</th>\n", "      <td>other</td>\n", "      <td>5425.0</td>\n", "      <td>13.0</td>\n", "      <td>275.0</td>\n", "      <td>13</td>\n", "      <td>5069.124424</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            location  total_sqft  bath  price  bhk  price_per_sqft\n", "5307  Neeladri Nagar      4000.0  12.0  160.0   10     4000.000000\n", "8534           other     12000.0  12.0  525.0   10     4375.000000\n", "8623           other     10000.0  16.0  550.0   16     5500.000000\n", "9363           other      6000.0  12.0  150.0   11     2500.000000\n", "9696           other      5425.0  13.0  275.0   13     5069.124424"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["df3[df3.bath>10]   # it is unusual to have 2 more bathrooms than bedrooms"]}, {"cell_type": "code", "execution_count": 87, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4aCCXuayuWjK", "outputId": "a96e55d5-8e50-49c1-d479-2d021a56e9ad"}, "outputs": [{"data": {"text/plain": ["(7282, 6)"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["df3=df3[df3.bath<df3.bhk +2]\n", "df3.shape"]}, {"cell_type": "code", "execution_count": 88, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZbvxSHkjyE8o", "outputId": "9969339e-6ef2-4124-ac3b-3762b4238736"}, "outputs": [], "source": ["df3.drop(['price_per_sqft'],axis=1,inplace=True) #droped price per sqft cause its not needed anymore"]}, {"cell_type": "code", "execution_count": 89, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 307}, "id": "juE4UqFnyUGm", "outputId": "2772b937-99db-4f5b-fe9b-e7a6df36bdfb"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>1st Block Jayanagar</th>\n", "      <th>1st Phase JP Nagar</th>\n", "      <th>2nd Phase Judicial Layout</th>\n", "      <th>2nd Stage Nagarbhavi</th>\n", "      <th>5th Block Hbr Layout</th>\n", "      <th>5th Phase JP Nagar</th>\n", "      <th>6th Phase JP Nagar</th>\n", "      <th>7th Phase JP Nagar</th>\n", "      <th>8th Phase JP Nagar</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON> Layout</th>\n", "      <th>Vishwa<PERSON>riya Layout</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Whitefield</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Yelahanka New Town</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>other</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 241 columns</p>\n", "</div>"], "text/plain": ["    Devarachikkanahalli  1st Block Jayanagar  1st Phase JP Nagar  \\\n", "1                     1                    0                   0   \n", "2                     1                    0                   0   \n", "3                     1                    0                   0   \n", "4                     1                    0                   0   \n", "5                     1                    0                   0   \n", "\n", "   2nd Phase Judicial Layout  2nd Stage Nagarbhavi  5th Block Hbr Layout  \\\n", "1                          0                     0                     0   \n", "2                          0                     0                     0   \n", "3                          0                     0                     0   \n", "4                          0                     0                     0   \n", "5                          0                     0                     0   \n", "\n", "   5th Phase JP Nagar  6th Phase JP Nagar  7th Phase JP Nagar  \\\n", "1                   0                   0                   0   \n", "2                   0                   0                   0   \n", "3                   0                   0                   0   \n", "4                   0                   0                   0   \n", "5                   0                   0                   0   \n", "\n", "   8th Phase JP Nagar  ...  <PERSON><PERSON><PERSON><PERSON><PERSON> Layout  Vishwapriya Layout  \\\n", "1                   0  ...                     0                   0   \n", "2                   0  ...                     0                   0   \n", "3                   0  ...                     0                   0   \n", "4                   0  ...                     0                   0   \n", "5                   0  ...                     0                   0   \n", "\n", "   Vittasandra  Whitefield  Yelachenahalli  Yelahanka  Yelahanka New Town  \\\n", "1            0           0               0          0                   0   \n", "2            0           0               0          0                   0   \n", "3            0           0               0          0                   0   \n", "4            0           0               0          0                   0   \n", "5            0           0               0          0                   0   \n", "\n", "   Yelenahalli  Yeshwanthpur  other  \n", "1            0             0      0  \n", "2            0             0      0  \n", "3            0             0      0  \n", "4            0             0      0  \n", "5            0             0      0  \n", "\n", "[5 rows x 241 columns]"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["# Model Building\n", "dummies=pd.get_dummies(df3.location,dtype=np.int8)\n", "dummies.head()"]}, {"cell_type": "code", "execution_count": 90, "metadata": {"id": "QtbUd8zGyxdc"}, "outputs": [], "source": ["df3=pd.concat([df3,dummies.drop(['other'],axis=1)],axis=1)"]}, {"cell_type": "code", "execution_count": 91, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 307}, "id": "QC5nPM_5z4Xn", "outputId": "************************************"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>1st Block Jayanagar</th>\n", "      <th>1st Phase JP Nagar</th>\n", "      <th>2nd Phase Judicial Layout</th>\n", "      <th>2nd Stage Nagarbhavi</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON> Layout</th>\n", "      <th>Vishwa<PERSON>riya Layout</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Whitefield</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Yelahanka New Town</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1250.0</td>\n", "      <td>2.0</td>\n", "      <td>40.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1200.0</td>\n", "      <td>2.0</td>\n", "      <td>83.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1170.0</td>\n", "      <td>2.0</td>\n", "      <td>40.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1425.0</td>\n", "      <td>2.0</td>\n", "      <td>65.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>947.0</td>\n", "      <td>2.0</td>\n", "      <td>43.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 245 columns</p>\n", "</div>"], "text/plain": ["               location  total_sqft  bath  price  bhk   Devarachikkanahalli  \\\n", "1   Devarachikkanahalli      1250.0   2.0   40.0    2                     1   \n", "2   Devarachikkanahalli      1200.0   2.0   83.0    2                     1   \n", "3   Devarachikkanahalli      1170.0   2.0   40.0    2                     1   \n", "4   Devarachikkanahalli      1425.0   2.0   65.0    3                     1   \n", "5   Devarachikkanahalli       947.0   2.0   43.0    2                     1   \n", "\n", "   1st Block Jayanagar  1st Phase JP Nagar  2nd Phase Judicial Layout  \\\n", "1                    0                   0                          0   \n", "2                    0                   0                          0   \n", "3                    0                   0                          0   \n", "4                    0                   0                          0   \n", "5                    0                   0                          0   \n", "\n", "   2nd Stage Nagarbhavi  ...  Vijay<PERSON><PERSON>  V<PERSON><PERSON><PERSON>rya Layout  \\\n", "1                     0  ...            0                     0   \n", "2                     0  ...            0                     0   \n", "3                     0  ...            0                     0   \n", "4                     0  ...            0                     0   \n", "5                     0  ...            0                     0   \n", "\n", "   Vishwapriya Layout  V<PERSON><PERSON>ndra  Whitefield  Yelachenahalli  Yelahanka  \\\n", "1                   0            0           0               0          0   \n", "2                   0            0           0               0          0   \n", "3                   0            0           0               0          0   \n", "4                   0            0           0               0          0   \n", "5                   0            0           0               0          0   \n", "\n", "   Yelahanka New Town  Yelenahalli  Yeshwanthpur  \n", "1                   0            0             0  \n", "2                   0            0             0  \n", "3                   0            0             0  \n", "4                   0            0             0  \n", "5                   0            0             0  \n", "\n", "[5 rows x 245 columns]"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["df3.head()"]}, {"cell_type": "code", "execution_count": 92, "metadata": {"id": "KjfCEGrWMput"}, "outputs": [], "source": ["df3.location=df3.location.astype(str)\n", "df3.location=df3.location.str.strip()\n", "df3.columns=df3.columns.str.strip()"]}, {"cell_type": "code", "execution_count": 93, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 307}, "id": "-uA8RfVOL-AC", "outputId": "c029068b-8a03-4977-ace6-cf96cf2caefb"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>location</th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>price</th>\n", "      <th>bhk</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>1st Block Jayanagar</th>\n", "      <th>1st Phase JP Nagar</th>\n", "      <th>2nd Phase Judicial Layout</th>\n", "      <th>2nd Stage Nagarbhavi</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON> Layout</th>\n", "      <th>Vishwa<PERSON>riya Layout</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Whitefield</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Yelahanka New Town</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1250.0</td>\n", "      <td>2.0</td>\n", "      <td>40.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1200.0</td>\n", "      <td>2.0</td>\n", "      <td>83.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1170.0</td>\n", "      <td>2.0</td>\n", "      <td>40.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>1425.0</td>\n", "      <td>2.0</td>\n", "      <td>65.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>947.0</td>\n", "      <td>2.0</td>\n", "      <td>43.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 245 columns</p>\n", "</div>"], "text/plain": ["              location  total_sqft  bath  price  bhk  Devarachikkanahalli  \\\n", "1  Devarachikkanahalli      1250.0   2.0   40.0    2                    1   \n", "2  Devarachikkanahalli      1200.0   2.0   83.0    2                    1   \n", "3  Devarachikkanahalli      1170.0   2.0   40.0    2                    1   \n", "4  Devarachikkanahalli      1425.0   2.0   65.0    3                    1   \n", "5  Devarachikkanahalli       947.0   2.0   43.0    2                    1   \n", "\n", "   1st Block Jayanagar  1st Phase JP Nagar  2nd Phase Judicial Layout  \\\n", "1                    0                   0                          0   \n", "2                    0                   0                          0   \n", "3                    0                   0                          0   \n", "4                    0                   0                          0   \n", "5                    0                   0                          0   \n", "\n", "   2nd Stage Nagarbhavi  ...  Vijay<PERSON><PERSON>  V<PERSON><PERSON><PERSON>rya Layout  \\\n", "1                     0  ...            0                     0   \n", "2                     0  ...            0                     0   \n", "3                     0  ...            0                     0   \n", "4                     0  ...            0                     0   \n", "5                     0  ...            0                     0   \n", "\n", "   Vishwapriya Layout  V<PERSON><PERSON>ndra  Whitefield  Yelachenahalli  Yelahanka  \\\n", "1                   0            0           0               0          0   \n", "2                   0            0           0               0          0   \n", "3                   0            0           0               0          0   \n", "4                   0            0           0               0          0   \n", "5                   0            0           0               0          0   \n", "\n", "   Yelahanka New Town  Yelenahalli  Yeshwanthpur  \n", "1                   0            0             0  \n", "2                   0            0             0  \n", "3                   0            0             0  \n", "4                   0            0             0  \n", "5                   0            0             0  \n", "\n", "[5 rows x 245 columns]"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["df3[df3.location=='Devarachikkanahalli'].head()"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/plain": ["(7282, 245)"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["df3.shape"]}, {"cell_type": "code", "execution_count": 95, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 307}, "id": "S6wd1sTmz6aq", "outputId": "89deff61-4fb6-4ae0-c54f-25767029b966"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_sqft</th>\n", "      <th>bath</th>\n", "      <th>bhk</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>1st Block Jayanagar</th>\n", "      <th>1st Phase JP Nagar</th>\n", "      <th>2nd Phase Judicial Layout</th>\n", "      <th>2nd Stage Nagarbhavi</th>\n", "      <th>5th Block Hbr Layout</th>\n", "      <th>5th Phase JP Nagar</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON> Layout</th>\n", "      <th>Vishwa<PERSON>riya Layout</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Whitefield</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Yelahanka New Town</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1250.0</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1200.0</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1170.0</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1425.0</td>\n", "      <td>2.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>947.0</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 243 columns</p>\n", "</div>"], "text/plain": ["   total_sqft  bath  bhk  Devarachikkanahalli  1st Block Jayanagar  \\\n", "1      1250.0   2.0    2                    1                    0   \n", "2      1200.0   2.0    2                    1                    0   \n", "3      1170.0   2.0    2                    1                    0   \n", "4      1425.0   2.0    3                    1                    0   \n", "5       947.0   2.0    2                    1                    0   \n", "\n", "   1st Phase JP Nagar  2nd Phase Judicial Layout  2nd Stage Nagarbhavi  \\\n", "1                   0                          0                     0   \n", "2                   0                          0                     0   \n", "3                   0                          0                     0   \n", "4                   0                          0                     0   \n", "5                   0                          0                     0   \n", "\n", "   5th Block Hbr Layout  5th Phase JP Nagar  ...  Vijayanagar  \\\n", "1                     0                   0  ...            0   \n", "2                     0                   0  ...            0   \n", "3                     0                   0  ...            0   \n", "4                     0                   0  ...            0   \n", "5                     0                   0  ...            0   \n", "\n", "   <PERSON><PERSON><PERSON><PERSON>rya Layout  Vishwapriya Layout  V<PERSON><PERSON><PERSON>  \\\n", "1                     0                   0            0           0   \n", "2                     0                   0            0           0   \n", "3                     0                   0            0           0   \n", "4                     0                   0            0           0   \n", "5                     0                   0            0           0   \n", "\n", "   Yelachenahalli  Yelahanka  Yelahanka New Town  Yelenahalli  Yeshwanthpur  \n", "1               0          0                   0            0             0  \n", "2               0          0                   0            0             0  \n", "3               0          0                   0            0             0  \n", "4               0          0                   0            0             0  \n", "5               0          0                   0            0             0  \n", "\n", "[5 rows x 243 columns]"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["x=df3.drop(['price','location'],axis=1)\n", "y=df3.price\n", "x.head()"]}, {"cell_type": "code", "execution_count": 108, "metadata": {"id": "3oGgHnQc0kRc"}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "xtrain,xtest,ytrain,ytest=train_test_split(x,y,train_size=0.8,random_state=643,shuffle=True)  # shuffle needs to be true as we had\n", "# we had data grouped by location and by default Kfold or train test split these split without shuffling.\n", "# OR USE shuffled_df = df.sample(frac=1, random_state=0).reset_index(drop=True) TO SHUFFLE DF BEFORE X Y INIT"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["score=0\n", "state=0\n", "for i in range(0,1000):\n", "    xtrain,xtest,ytrain,ytest=train_test_split(x,y,train_size=0.8,random_state=i,shuffle=True)\n", "    lr=LinearRegression()\n", "    lr.fit(xtrain,ytrain)\n", "    if score<lr.score(xtest,ytest):\n", "        score=lr.score(xtest,ytest)\n", "        state=i\n"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["643 \n", " 0.9052545350840538\n"]}], "source": ["print(state,\"\\n\",score)"]}, {"cell_type": "code", "execution_count": 109, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6DA7jOqZ1SKB", "outputId": "e821b4df-d562-4cd9-9dba-25cee54ed583"}, "outputs": [{"data": {"text/plain": ["0.9052545350840538"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import LinearRegression  # data is continous not categorical(rank)(nominal or ordinal)\n", "lr=LinearRegression()\n", "lr.fit(xtrain,ytrain)\n", "pred=lr.predict(xtest)\n", "lr.score(xtest,ytest)"]}, {"cell_type": "code", "execution_count": 110, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AyuXbaof1ohY", "outputId": "4370144d-3459-433a-a2a5-c1fac58113e3"}, "outputs": [{"data": {"text/plain": ["0.9085888758293169"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.metrics import r2_score\n", "score=r2_score(pred,ytest)\n", "score"]}, {"cell_type": "code", "execution_count": 111, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b6yOe6Iq3UC9", "outputId": "94fce905-b240-43d0-907e-c0beb98f5ba9"}, "outputs": [{"data": {"text/plain": ["0.9052545350840538"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["lr.score(xtest,ytest)"]}, {"cell_type": "code", "execution_count": 112, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iu-8K08p3imS", "outputId": "67ed4db1-0723-405f-9709-4dd5e6f798bf"}, "outputs": [{"data": {"text/plain": ["array([0.78235958, 0.84957157, 0.83058247, 0.86589344, 0.83894461])"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.model_selection import ShuffleSplit\n", "from sklearn.model_selection import cross_val_score\n", "\n", "cv = ShuffleSplit(n_splits=5, test_size=0.2, random_state=0)\n", "\n", "cross_val_score(LinearRegression(), x, y, cv=cv)     # using shuffle split is essential here"]}, {"cell_type": "code", "execution_count": 113, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "OfxITQp55gab", "outputId": "57628ae1-c620-44cd-87cf-80641c9d3326"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>best_score</th>\n", "      <th>best_params</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>linear_regression</td>\n", "      <td>0.833470</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>lasso</td>\n", "      <td>0.682504</td>\n", "      <td>{'alpha': 1, 'selection': 'cyclic'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>decision_tree</td>\n", "      <td>0.747968</td>\n", "      <td>{'criterion': 'squared_error', 'splitter': 'be...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               model  best_score  \\\n", "0  linear_regression    0.833470   \n", "1              lasso    0.682504   \n", "2      decision_tree    0.747968   \n", "\n", "                                         best_params  \n", "0                                                 {}  \n", "1                {'alpha': 1, 'selection': 'cyclic'}  \n", "2  {'criterion': 'squared_error', 'splitter': 'be...  "]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.model_selection import GridSearchCV\n", "from sklearn.linear_model import Lasso\n", "from sklearn.tree import DecisionTreeRegressor\n", "def find_best_model_using_gridsearchcv(X,y):\n", "    algos = {\n", "        'linear_regression' : {\n", "            'model': LinearRegression(),\n", "            'params': {\n", "\n", "            }\n", "        },\n", "        'lasso': {\n", "            'model': <PERSON><PERSON>(),\n", "            'params': {\n", "                'alpha': [1,2],\n", "                'selection': ['random', 'cyclic']\n", "            }\n", "        },\n", "        'decision_tree': {\n", "            'model': DecisionTreeRegressor(),\n", "            'params': {\n", "                'criterion' : ['squared_error','friedman_mse'],\n", "                'splitter': ['best','random']\n", "            }\n", "        }\n", "    }\n", "    scores = []\n", "    cv = ShuffleSplit(n_splits=5, test_size=0.2, random_state=0) # COULD HAVE AVOIDED USING THIS IF HAD SHUFFLED DF\n", "    for algo_name, config in algos.items():\n", "        gs =  GridSearchCV(config['model'], config['params'], cv=cv, n_jobs=9, return_train_score=False)\n", "        gs.fit(x,y)\n", "        scores.append({\n", "            'model': algo_name,\n", "            'best_score': gs.best_score_,\n", "            'best_params': gs.best_params_\n", "        })\n", "\n", "    return pd.DataFrame(scores,columns=['model','best_score','best_params'])\n", "\n", "find_best_model_using_gridsearchcv(x,y)"]}, {"cell_type": "code", "execution_count": 115, "metadata": {"id": "0RLPmeL1_-2v"}, "outputs": [], "source": ["import pickle\n", "with open(\"bengaluru_House_Data.pkl\",'wb') as f:\n", "  pickle.dump(lr,f)"]}, {"cell_type": "code", "execution_count": 116, "metadata": {"id": "9oD2_Y7-AOys"}, "outputs": [], "source": ["import json # need columns list for application\n", "columns = {\n", "    'data_columns' : [col.lower() for col in x.columns]\n", "}\n", "with open(\"columns.json\",\"w\") as f:\n", "    f.write(json.dumps(columns))"]}, {"cell_type": "code", "execution_count": 145, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "T85DEWxAbKcD", "outputId": "65b975bc-4444-4ac1-d0f1-ccdd98d3b390"}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["np.where(x.columns=='Dev<PERSON><PERSON><PERSON>alli')[0][0]"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [{"data": {"text/plain": ["['total_sqft',\n", " 'bath',\n", " 'bhk',\n", " 'de<PERSON><PERSON><PERSON><PERSON><PERSON>i',\n", " '1st block jayanagar',\n", " '1st phase jp nagar',\n", " '2nd phase judicial layout',\n", " '2nd stage nagarbhavi',\n", " '5th block hbr layout',\n", " '5th phase jp nagar',\n", " '6th phase jp nagar',\n", " '7th phase jp nagar',\n", " '8th phase jp nagar',\n", " '9th phase jp nagar',\n", " 'aecs layout',\n", " 'abbigere',\n", " 'akshaya nagar',\n", " 'ambalipura',\n", " 'ambedkar nagar',\n", " 'amru<PERSON><PERSON>i',\n", " 'anandapura',\n", " 'ananth nagar',\n", " 'anekal',\n", " 'anjanapura',\n", " 'ardendale',\n", " 'arekere',\n", " 'attibele',\n", " 'beml layout',\n", " 'btm 2nd stage',\n", " 'btm layout',\n", " 'babusapalaya',\n", " 'badavala nagar',\n", " 'balagere',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'banashankari stage ii',\n", " 'banashankari stage iii',\n", " 'ban<PERSON><PERSON>kari stage v',\n", " '<PERSON><PERSON><PERSON><PERSON> stage vi',\n", " 'banaswadi',\n", " 'banjara layout',\n", " '<PERSON><PERSON><PERSON>a',\n", " 'bannerghatta road',\n", " 'basavangudi',\n", " 'basa<PERSON><PERSON>ra nagar',\n", " 'battarahalli',\n", " 'begur',\n", " 'begur road',\n", " 'bellandur',\n", " 'benson town',\n", " 'bharathi nagar',\n", " 'bhogan<PERSON>i',\n", " 'bill<PERSON><PERSON>i',\n", " 'binny pete',\n", " 'bisu<PERSON><PERSON>i',\n", " 'bommanahalli',\n", " 'bom<PERSON><PERSON>',\n", " 'bommasandra industrial area',\n", " 'bommen<PERSON><PERSON>i',\n", " 'brookefield',\n", " 'budigere',\n", " 'cv raman nagar',\n", " 'chamrajpet',\n", " 'chandapura',\n", " 'channa<PERSON><PERSON>',\n", " 'chikka tirupathi',\n", " 'chikka<PERSON>var',\n", " 'chik<PERSON><PERSON><PERSON>',\n", " 'chood<PERSON><PERSON>',\n", " 'cooke town',\n", " 'cox town',\n", " 'cunningham road',\n", " 'dasanapura',\n", " 'dasara<PERSON>i',\n", " 'de<PERSON><PERSON><PERSON>',\n", " 'dodda nekkundi',\n", " 'doddaballapur',\n", " 'dodd<PERSON><PERSON><PERSON>',\n", " 'doddathoguru',\n", " 'domlur',\n", " 'do<PERSON><PERSON><PERSON>',\n", " 'epip zone',\n", " 'electronic city',\n", " 'electronic city phase ii',\n", " 'electronics city phase 1',\n", " 'frazer town',\n", " 'gm palaya',\n", " 'garuda<PERSON>r palya',\n", " 'giri nagar',\n", " 'gollarapalya hosahalli',\n", " 'gottigere',\n", " 'green glen layout',\n", " 'gubbalala',\n", " 'gunjur',\n", " 'hal 2nd stage',\n", " 'hbr layout',\n", " 'hrbr layout',\n", " 'hsr layout',\n", " 'haralur road',\n", " 'harlur',\n", " 'hebbal',\n", " 'hebbal kempa<PERSON>',\n", " 'hegde nagar',\n", " 'hennur',\n", " 'hennur road',\n", " 'hoodi',\n", " 'horamavu agara',\n", " 'horamavu banaswadi',\n", " 'hormavu',\n", " 'hosa road',\n", " 'hosa<PERSON><PERSON><PERSON>i',\n", " 'hoskote',\n", " 'hosur road',\n", " 'hulimavu',\n", " 'isro layout',\n", " 'itpl',\n", " 'iblur village',\n", " 'indira nagar',\n", " 'jp nagar',\n", " 'jakkur',\n", " 'jalahalli',\n", " 'jalahalli east',\n", " 'jigani',\n", " 'judicial layout',\n", " 'kr puram',\n", " 'ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'kadugodi',\n", " 'kaggadasapura',\n", " 'kaggalipura',\n", " 'kaiko<PERSON><PERSON>i',\n", " 'kalena a<PERSON>',\n", " 'kalyan nagar',\n", " 'kambipura',\n", " 'kammanah<PERSON>i',\n", " 'kamma<PERSON><PERSON>',\n", " 'kanakapura',\n", " 'kanakpura road',\n", " 'kannamangala',\n", " 'karuna nagar',\n", " 'kasavanhalli',\n", " 'kasturi nagar',\n", " 'ka<PERSON><PERSON><PERSON><PERSON>',\n", " 'kaval by<PERSON>',\n", " 'ken<PERSON><PERSON><PERSON><PERSON>',\n", " 'kengeri',\n", " 'kengeri satellite town',\n", " 'kereguddadahalli',\n", " 'kodichi<PERSON>alli',\n", " 'kodigehaali',\n", " 'kodigehalli',\n", " 'kodihalli',\n", " 'kogilu',\n", " 'konanakunte',\n", " 'koramangala',\n", " 'kothannur',\n", " 'kothanur',\n", " 'kudlu',\n", " 'kudlu gate',\n", " 'kuma<PERSON><PERSON> layout',\n", " 'kundalahalli',\n", " 'lb shastri nagar',\n", " 'laggere',\n", " 'laks<PERSON><PERSON><PERSON><PERSON> pura',\n", " 'l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'magadi road',\n", " 'ma<PERSON><PERSON><PERSON>',\n", " 'mahalakshmi layout',\n", " 'mallasandra',\n", " 'mall<PERSON><PERSON>ya',\n", " 'malleshwaram',\n", " 'maratha<PERSON>i',\n", " 'ma<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'marsur',\n", " 'mico layout',\n", " 'munnekollal',\n", " 'muru<PERSON><PERSON><PERSON><PERSON>',\n", " 'mysore road',\n", " 'ngr layout',\n", " 'nri layout',\n", " 'nagarbhavi',\n", " 'nagas<PERSON>',\n", " 'nagavara',\n", " 'nagavarapalya',\n", " 'narayanapura',\n", " 'ne<PERSON><PERSON>i nagar',\n", " 'nehru nagar',\n", " 'ombr layout',\n", " 'old airport road',\n", " 'old madras road',\n", " 'padmanabhanagar',\n", " 'pai layout',\n", " 'panathur',\n", " 'parappana agrahara',\n", " 'pat<PERSON><PERSON>r a<PERSON>',\n", " 'poorna pragna layout',\n", " 'prithvi layout',\n", " 'r.t. nagar',\n", " 'rache<PERSON><PERSON><PERSON>',\n", " 'raja raj<PERSON><PERSON> nagar',\n", " 'rajaji nagar',\n", " 'rajiv nagar',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'ram<PERSON><PERSON><PERSON> nagar',\n", " 'ray<PERSON><PERSON>',\n", " 'sahakara nagar',\n", " 'sanjay nagar',\n", " 'sarakki nagar',\n", " 'sarjapur',\n", " 'sarjapur  road',\n", " 'sarjapura - attibele road',\n", " 'sector 2 hsr layout',\n", " 'sector 7 hsr layout',\n", " 'seegehalli',\n", " 'shampura',\n", " 's<PERSON>ji nagar',\n", " 'singasandra',\n", " 'so<PERSON><PERSON>ra palya',\n", " 'sompura',\n", " 'son<PERSON><PERSON><PERSON><PERSON>',\n", " 'subramanyapura',\n", " 'sultan palaya',\n", " 'tc palaya',\n", " 'talaghattapura',\n", " '<PERSON><PERSON><PERSON>',\n", " 'thigalar<PERSON>lya',\n", " 'thuba<PERSON><PERSON>i',\n", " 'tindlu',\n", " 'tumkur road',\n", " 'ulsoor',\n", " 'utta<PERSON><PERSON><PERSON>',\n", " 'varthur',\n", " 'varthur road',\n", " 'vasanthapura',\n", " 'vidyaranyapura',\n", " 'vijayanagar',\n", " 'v<PERSON><PERSON><PERSON><PERSON> layout',\n", " 'vish<PERSON><PERSON><PERSON> layout',\n", " 'vittasandra',\n", " 'whitefield',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'yelahanka',\n", " 'yelahanka new town',\n", " 'yelenahalli',\n", " 'yeshwanthpur']"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["columns['data_columns']"]}, {"cell_type": "code", "execution_count": 188, "metadata": {"id": "XOsP56tzcEjz"}, "outputs": [], "source": ["def predict_price(location,total_sqft,bath,bhk):\n", "  location = location.strip().lower()\n", "  \n", "  data_columns_lower = [col.lower() for col in columns['data_columns']]\n", "  if location not in data_columns_lower:\n", "    print(f\"Location '{location}' not found.\")\n", "    return None\n", "  loc_index=data_columns_lower.index(location)\n", "  a=np.zeros(len(columns[\"data_columns\"]))\n", "  a[0]=total_sqft\n", "  a[1]=bath\n", "  a[2]=bhk\n", "  if loc_index>=0:\n", "    a[loc_index]=1\n", "  return lr.predict([a])[0]"]}, {"cell_type": "code", "execution_count": 189, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KT3CHkAxev6L", "outputId": "7c831abc-b6b5-4b7a-93bd-e871323cc1d1"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["86.90502064984467"]}, "execution_count": 189, "metadata": {}, "output_type": "execute_result"}], "source": ["predict_price('1st Phase JP Nagar',1000,2,2)"]}, {"cell_type": "code", "execution_count": 190, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JXfhpzulfCvM", "outputId": "946ea301-2ea5-4a7b-c512-70ef55c74b3b"}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 190, "metadata": {}, "output_type": "execute_result"}], "source": ["set(x.columns)-set(xtrain.columns)"]}, {"cell_type": "code", "execution_count": 191, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S9eDJMIhqFlM", "outputId": "6ea0d36f-88c1-40a8-c34e-4ee1c9cefb8d"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["179.90960994365096"]}, "execution_count": 191, "metadata": {}, "output_type": "execute_result"}], "source": ["predict_price('Indira Nagar',1000,3,3)"]}, {"cell_type": "code", "execution_count": 193, "metadata": {"id": "uRRGGqKGr4sS"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["37.41138408790596"]}, "execution_count": 193, "metadata": {}, "output_type": "execute_result"}], "source": ["predict_price('akshaya nagar',1000,3,3)  # 2,2 more costy than 3,3 cause sqft same so congested"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 0}